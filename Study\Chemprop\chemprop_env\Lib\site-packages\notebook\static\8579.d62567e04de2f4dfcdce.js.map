{"version": 3, "file": "8579.d62567e04de2f4dfcdce.js?v=d62567e04de2f4dfcdce", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAC8G;AACO;AAClE;AACiB;AACI;AACf;AACR;AAC+F;AACjF;AACO;AACwF;AAChG;AACV;AACoB;AACzB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gCAAgC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,+DAAU,EAAE,gEAAW;AACtC;AACA,6BAA6B,uEAAW;AACxC,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,+DAAU;AACxB;AACA,6BAA6B,uEAAW;AACxC,+BAA+B,SAAS;AACxC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,6DAAU;AAClC;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA,yBAAyB,oDAAM,GAAG,MAAM;AACxC,QAAQ,yEAAW;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,qCAAqC,SAAS;AAC9C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,4DAAO,EAAE,oEAAgB;AACxC,eAAe,yEAAgB;AAC/B;AACA,gBAAgB,wBAAwB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,0BAA0B,gCAAgC;AAC1D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,2DAAS;AACxB;AACA;AACA;AACA;AACA,qBAAqB,6DAAU;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oDAAM;AAC/B,kBAAkB,0DAAQ;AAC1B;AACA,sCAAsC,WAAW;AACjD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gEAAW;AAC1B,eAAe,iEAAe;AAC9B;AACA;AACA,wBAAwB,6DAAU;AAClC;AACA;AACA;AACA,4BAA4B,yDAAM;AAClC,aAAa;AACb,SAAS;AACT,qBAAqB,6DAAU;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,yDAAM;AACtC;AACA,aAAa;AACb,SAAS;AACT;AACA,8BAA8B,+CAA+C;AAC7E,8BAA8B,gDAAgD;AAC9E;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,+EAAmB;AACjC;AACA,eAAe,qFAAyB;AACxC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,oEAAe;AAC7B;AACA,6BAA6B,uEAAW;AACxC,+BAA+B,UAAU;AACzC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,uEAAmB;AACjC;AACA,QAAQ,oEAAgB;AACxB,QAAQ,oEAAgB;AACxB,QAAQ,4DAAU;AAClB,QAAQ,mEAAe;AACvB,QAAQ,gEAAW;AACnB,QAAQ,+EAAmB;AAC3B;AACA;AACA,mFAAmF,mEAAc;AACjG,2GAA2G,qFAAyB;AACpI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,gBAAgB;AACrD;AACA,wCAAwC,6DAAU;AAClD;AACA,oCAAoC,yDAAM;AAC1C;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb;AACA,mBAAmB,sEAAkB;AACrC,8BAA8B,6EAAyB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0EAAc;AAC5B,eAAe,yEAAgB;AAC/B;AACA,mCAAmC,yEAAa;AAChD,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,+DAAa;AAC3B;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mEAAkB;AAC7C;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,+DAAU;AACxB;AACA,6BAA6B,uEAAW;AACxC,+BAA+B,WAAW;AAC1C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0EAAc;AAC7B;AACA;AACA;AACA,mCAAmC,6DAAY;AAC/C;AACA;AACA,qCAAqC,0DAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,mEAAc;AACtD;AACA,qCAAqC,0DAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0EAAc,EAAE,gEAAW;AAC1C,eAAe,oEAAgB,EAAE,4DAAO,EAAE,wEAAsB;AAChE;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA,mCAAmC,oDAAM,GAAG,MAAM;AAClD;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iDAAiD,mEAAc;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,mBAAmB;AACnF,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,oEAAY;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,0DAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,MAAM,GAAG,QAAQ;AACzD;AACA,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0EAAc,EAAE,gEAAW;AAC1C,eAAe,yEAAgB,EAAE,iEAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,8BAA8B,iDAAiD;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0EAAc,EAAE,gEAAW;AAC1C,eAAe,2DAAS,EAAE,iEAAe;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,kDAAI,GAAG,wBAAwB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,qBAAqB;AAC5D;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,4EAAgB;AAC7D;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,4DAAO;AACtB,cAAc,oEAAe;AAC7B;AACA,gBAAgB,WAAW;AAC3B,wBAAwB,8DAAa;AACrC,6BAA6B,+DAAe;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,yDAAM;AACpC;AACA;AACA;AACA;AACA;AACA,mCAAmC,eAAe,6DAAU,wBAAwB;AACpF,aAAa;AACb,SAAS;AACT,kCAAkC,uDAAuD;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mEAAkB;AACtC;AACA,SAAS;AACT,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,4DAAO;AACtB,cAAc,qEAAgB;AAC9B;AACA;AACA,6BAA6B,6DAAU;AACvC,6BAA6B,yDAAM,MAAM,6DAAU,sCAAsC,yDAAM;AAC/F,wCAAwC,mBAAmB;AAC3D;AACA,gBAAgB,6DAAU;AAC1B;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0EAAc,EAAE,gEAAW;AAC1C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gEAAW;AAC1B,eAAe,iEAAe,EAAE,0EAAc;AAC9C;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,8BAA8B,iDAAiD;AAC/E;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { ILabStatus, IRouter, ITreePathUpdater, JupyterFrontEnd, JupyterLab, } from '@jupyterlab/application';\nimport { DOMUtils, ICommandPalette, ISanitizer, ISplashScreen, IToolbarWidgetRegistry, } from '@jupyterlab/apputils';\nimport { ConsolePanel } from '@jupyterlab/console';\nimport { PageConfig, PathExt, URLExt } from '@jupyterlab/coreutils';\nimport { IDocumentManager, renameDialog } from '@jupyterlab/docmanager';\nimport { DocumentWidget } from '@jupyterlab/docregistry';\nimport { IMainMenu } from '@jupyterlab/mainmenu';\nimport { ILatexTypesetter, IMarkdownParser, IRenderMimeRegistry, RenderMimeRegistry, standardRendererFactories, } from '@jupyterlab/rendermime';\nimport { ISettingRegistry } from '@jupyterlab/settingregistry';\nimport { ITranslator, nullTranslator } from '@jupyterlab/translation';\nimport { NotebookApp, NotebookShell, INotebookShell, SidePanelPalette, INotebookPathOpener, defaultNotebookPathOpener, } from '@jupyter-notebook/application';\nimport { jupyterIcon } from '@jupyter-notebook/ui-components';\nimport { PromiseDelegate } from '@lumino/coreutils';\nimport { DisposableDelegate, DisposableSet, } from '@lumino/disposable';\nimport { Menu, Widget } from '@lumino/widgets';\n/**\n * A regular expression to match path to notebooks and documents\n */\nconst TREE_PATTERN = new RegExp('/(notebooks|edit)/(.*)');\n/**\n * A regular expression to suppress the file extension from display for .ipynb files.\n */\nconst STRIP_IPYNB = /\\.ipynb$/;\n/**\n * The JupyterLab document manager plugin id.\n */\nconst JUPYTERLAB_DOCMANAGER_PLUGIN_ID = '@jupyterlab/docmanager-extension:plugin';\n/**\n * The command IDs used by the application plugin.\n */\nvar CommandIDs;\n(function (CommandIDs) {\n    /**\n     * Duplicate the current document and open the new document\n     */\n    CommandIDs.duplicate = 'application:duplicate';\n    /**\n     * Handle local links\n     */\n    CommandIDs.handleLink = 'application:handle-local-link';\n    /**\n     * Toggle Top Bar visibility\n     */\n    CommandIDs.toggleTop = 'application:toggle-top';\n    /**\n     * Toggle side panel visibility\n     */\n    CommandIDs.togglePanel = 'application:toggle-panel';\n    /**\n     * Toggle the Zen mode\n     */\n    CommandIDs.toggleZen = 'application:toggle-zen';\n    /**\n     * Open JupyterLab\n     */\n    CommandIDs.openLab = 'application:open-lab';\n    /**\n     * Open the tree page.\n     */\n    CommandIDs.openTree = 'application:open-tree';\n    /**\n     * Rename the current document\n     */\n    CommandIDs.rename = 'application:rename';\n    /**\n     * Resolve tree path\n     */\n    CommandIDs.resolveTree = 'application:resolve-tree';\n})(CommandIDs || (CommandIDs = {}));\n/**\n * Check if the application is dirty before closing the browser tab.\n */\nconst dirty = {\n    id: '@jupyter-notebook/application-extension:dirty',\n    description: 'Check if the application is dirty before closing the browser tab.',\n    autoStart: true,\n    requires: [ILabStatus, ITranslator],\n    activate: (app, status, translator) => {\n        if (!(app instanceof NotebookApp)) {\n            throw new Error(`${dirty.id} must be activated in Jupyter Notebook.`);\n        }\n        const trans = translator.load('notebook');\n        const message = trans.__('Are you sure you want to exit Jupyter Notebook?\\n\\nAny unsaved changes will be lost.');\n        window.addEventListener('beforeunload', (event) => {\n            if (app.status.isDirty) {\n                return (event.returnValue = message);\n            }\n        });\n    },\n};\n/**\n * The application info.\n */\nconst info = {\n    id: '@jupyter-notebook/application-extension:info',\n    autoStart: true,\n    provides: JupyterLab.IInfo,\n    activate: (app) => {\n        if (!(app instanceof NotebookApp)) {\n            throw new Error(`${info.id} must be activated in Jupyter Notebook.`);\n        }\n        return app.info;\n    },\n};\n/**\n * The logo plugin.\n */\nconst logo = {\n    id: '@jupyter-notebook/application-extension:logo',\n    description: 'The logo plugin.',\n    autoStart: true,\n    activate: (app) => {\n        const baseUrl = PageConfig.getBaseUrl();\n        const node = document.createElement('a');\n        node.href = `${baseUrl}tree`;\n        node.target = '_blank';\n        node.rel = 'noopener noreferrer';\n        const logo = new Widget({ node });\n        jupyterIcon.element({\n            container: node,\n            elementPosition: 'center',\n            padding: '2px 2px 2px 8px',\n            height: '28px',\n            width: 'auto',\n            cursor: 'pointer',\n            margin: 'auto',\n        });\n        logo.id = 'jp-NotebookLogo';\n        app.shell.add(logo, 'top', { rank: 0 });\n    },\n};\n/**\n * A plugin to open documents in the main area.\n */\nconst opener = {\n    id: '@jupyter-notebook/application-extension:opener',\n    description: 'A plugin to open documents in the main area.',\n    autoStart: true,\n    requires: [IRouter, IDocumentManager],\n    optional: [ISettingRegistry],\n    activate: (app, router, docManager, settingRegistry) => {\n        const { commands, docRegistry } = app;\n        const command = 'router:tree';\n        commands.addCommand(command, {\n            execute: (args) => {\n                var _a;\n                const parsed = args;\n                const matches = (_a = parsed.path.match(TREE_PATTERN)) !== null && _a !== void 0 ? _a : [];\n                const [, , path] = matches;\n                if (!path) {\n                    return;\n                }\n                app.started.then(async () => {\n                    var _a;\n                    const file = decodeURIComponent(path);\n                    const urlParams = new URLSearchParams(parsed.search);\n                    let defaultFactory = docRegistry.defaultWidgetFactory(path).name;\n                    // Explicitly get the default viewers from the settings because\n                    // JupyterLab might not have had the time to load the settings yet (race condition)\n                    // Relevant code: https://github.com/jupyterlab/jupyterlab/blob/d56ff811f39b3c10c6d8b6eb27a94624b753eb53/packages/docmanager-extension/src/index.tsx#L265-L293\n                    if (settingRegistry) {\n                        const settings = await settingRegistry.load(JUPYTERLAB_DOCMANAGER_PLUGIN_ID);\n                        const defaultViewers = settings.get('defaultViewers').composite;\n                        // get the file types for the path\n                        const types = docRegistry.getFileTypesForPath(path);\n                        // for each file type, check if there is a default viewer and if it\n                        // is available in the docRegistry. If it is the case, use it as the\n                        // default factory\n                        types.forEach((ft) => {\n                            if (defaultViewers[ft.name] !== undefined &&\n                                docRegistry.getWidgetFactory(defaultViewers[ft.name])) {\n                                defaultFactory = defaultViewers[ft.name];\n                            }\n                        });\n                    }\n                    const factory = (_a = urlParams.get('factory')) !== null && _a !== void 0 ? _a : defaultFactory;\n                    docManager.open(file, factory, undefined, {\n                        ref: '_noref',\n                    });\n                });\n            },\n        });\n        router.register({ command, pattern: TREE_PATTERN });\n    },\n};\n/**\n * A plugin to customize menus\n */\nconst menus = {\n    id: '@jupyter-notebook/application-extension:menus',\n    description: 'A plugin to customize menus.',\n    requires: [IMainMenu],\n    autoStart: true,\n    activate: (app, menu) => {\n        // always disable the Tabs menu\n        menu.tabsMenu.dispose();\n        const page = PageConfig.getOption('notebookPage');\n        switch (page) {\n            case 'consoles':\n            case 'terminals':\n            case 'tree':\n                menu.editMenu.dispose();\n                menu.kernelMenu.dispose();\n                menu.runMenu.dispose();\n                break;\n            case 'edit':\n                menu.kernelMenu.dispose();\n                menu.runMenu.dispose();\n                break;\n            default:\n                break;\n        }\n    },\n};\n/**\n * A plugin to provide a spacer at rank 900 in the menu area\n */\nconst menuSpacer = {\n    id: '@jupyter-notebook/application-extension:menu-spacer',\n    description: 'A plugin to provide a spacer at rank 900 in the menu area.',\n    autoStart: true,\n    activate: (app) => {\n        const menu = new Widget();\n        menu.id = DOMUtils.createDomID();\n        menu.addClass('jp-NotebookSpacer');\n        app.shell.add(menu, 'menu', { rank: 900 });\n    },\n};\n/**\n * Add commands to open the tree and running pages.\n */\nconst pages = {\n    id: '@jupyter-notebook/application-extension:pages',\n    description: 'Add commands to open the tree and running pages.',\n    autoStart: true,\n    requires: [ITranslator],\n    optional: [ICommandPalette],\n    activate: (app, translator, palette) => {\n        const trans = translator.load('notebook');\n        const baseUrl = PageConfig.getBaseUrl();\n        app.commands.addCommand(CommandIDs.openLab, {\n            label: trans.__('Open JupyterLab'),\n            execute: () => {\n                window.open(URLExt.join(baseUrl, 'lab'));\n            },\n        });\n        const page = PageConfig.getOption('notebookPage');\n        app.commands.addCommand(CommandIDs.openTree, {\n            label: trans.__('File Browser'),\n            execute: () => {\n                if (page === 'tree') {\n                    app.commands.execute('filebrowser:activate');\n                }\n                else {\n                    window.open(URLExt.join(baseUrl, 'tree'));\n                }\n            },\n        });\n        if (palette) {\n            palette.addItem({ command: CommandIDs.openLab, category: 'View' });\n            palette.addItem({ command: CommandIDs.openTree, category: 'View' });\n        }\n    },\n};\n/**\n * A plugin to open paths in new browser tabs.\n */\nconst pathOpener = {\n    id: '@jupyter-notebook/application-extension:path-opener',\n    description: 'A plugin to open paths in new browser tabs.',\n    autoStart: true,\n    provides: INotebookPathOpener,\n    activate: (app) => {\n        return defaultNotebookPathOpener;\n    },\n};\n/**\n * The default paths for a Jupyter Notebook app.\n */\nconst paths = {\n    id: '@jupyter-notebook/application-extension:paths',\n    description: 'The default paths for a Jupyter Notebook app.',\n    autoStart: true,\n    provides: JupyterFrontEnd.IPaths,\n    activate: (app) => {\n        if (!(app instanceof NotebookApp)) {\n            throw new Error(`${paths.id} must be activated in Jupyter Notebook.`);\n        }\n        return app.paths;\n    },\n};\n/**\n * A plugin providing a rendermime registry.\n */\nconst rendermime = {\n    id: '@jupyter-notebook/application-extension:rendermime',\n    description: 'A plugin providing a rendermime registry.',\n    autoStart: true,\n    provides: IRenderMimeRegistry,\n    optional: [\n        IDocumentManager,\n        ILatexTypesetter,\n        ISanitizer,\n        IMarkdownParser,\n        ITranslator,\n        INotebookPathOpener,\n    ],\n    activate: (app, docManager, latexTypesetter, sanitizer, markdownParser, translator, notebookPathOpener) => {\n        const trans = (translator !== null && translator !== void 0 ? translator : nullTranslator).load('jupyterlab');\n        const opener = notebookPathOpener !== null && notebookPathOpener !== void 0 ? notebookPathOpener : defaultNotebookPathOpener;\n        if (docManager) {\n            app.commands.addCommand(CommandIDs.handleLink, {\n                label: trans.__('Handle Local Link'),\n                execute: (args) => {\n                    const path = args['path'];\n                    if (path === undefined || path === null) {\n                        return;\n                    }\n                    return docManager.services.contents\n                        .get(path, { content: false })\n                        .then((model) => {\n                        const baseUrl = PageConfig.getBaseUrl();\n                        opener.open({\n                            prefix: URLExt.join(baseUrl, 'tree'),\n                            path: model.path,\n                            target: '_blank',\n                        });\n                    });\n                },\n            });\n        }\n        return new RenderMimeRegistry({\n            initialFactories: standardRendererFactories,\n            linkHandler: !docManager\n                ? undefined\n                : {\n                    handleLink: (node, path, id) => {\n                        // If node has the download attribute explicitly set, use the\n                        // default browser downloading behavior.\n                        if (node.tagName === 'A' && node.hasAttribute('download')) {\n                            return;\n                        }\n                        app.commandLinker.connectNode(node, CommandIDs.handleLink, {\n                            path,\n                            id,\n                        });\n                    },\n                },\n            latexTypesetter: latexTypesetter !== null && latexTypesetter !== void 0 ? latexTypesetter : undefined,\n            markdownParser: markdownParser !== null && markdownParser !== void 0 ? markdownParser : undefined,\n            translator: translator !== null && translator !== void 0 ? translator : undefined,\n            sanitizer: sanitizer !== null && sanitizer !== void 0 ? sanitizer : undefined,\n        });\n    },\n};\n/**\n * The default Jupyter Notebook application shell.\n */\nconst shell = {\n    id: '@jupyter-notebook/application-extension:shell',\n    description: 'The default Jupyter Notebook application shell.',\n    autoStart: true,\n    provides: INotebookShell,\n    optional: [ISettingRegistry],\n    activate: (app, settingRegistry) => {\n        if (!(app.shell instanceof NotebookShell)) {\n            throw new Error(`${shell.id} did not find a NotebookShell instance.`);\n        }\n        const notebookShell = app.shell;\n        if (settingRegistry) {\n            settingRegistry\n                .load(shell.id)\n                .then((settings) => {\n                // Add a layer of customization to support app shell mode\n                const customLayout = settings.composite['layout'];\n                // Restore the layout.\n                void notebookShell.restoreLayout(customLayout);\n            })\n                .catch((reason) => {\n                console.error('Fail to load settings for the layout restorer.');\n                console.error(reason);\n            });\n        }\n        return notebookShell;\n    },\n};\n/**\n * The default splash screen provider.\n */\nconst splash = {\n    id: '@jupyter-notebook/application-extension:splash',\n    description: 'Provides an empty splash screen.',\n    autoStart: true,\n    provides: ISplashScreen,\n    activate: (app) => {\n        const { restored } = app;\n        const splash = document.createElement('div');\n        splash.style.position = 'absolute';\n        splash.style.width = '100%';\n        splash.style.height = '100%';\n        splash.style.zIndex = '10';\n        return {\n            show: (light = true) => {\n                splash.style.backgroundColor = light ? 'white' : '#111111';\n                document.body.appendChild(splash);\n                return new DisposableDelegate(async () => {\n                    await restored;\n                    document.body.removeChild(splash);\n                });\n            },\n        };\n    },\n};\n/**\n * The default JupyterLab application status provider.\n */\nconst status = {\n    id: '@jupyter-notebook/application-extension:status',\n    description: 'The default JupyterLab application status provider.',\n    autoStart: true,\n    provides: ILabStatus,\n    activate: (app) => {\n        if (!(app instanceof NotebookApp)) {\n            throw new Error(`${status.id} must be activated in Jupyter Notebook.`);\n        }\n        return app.status;\n    },\n};\n/**\n * A plugin to display the document title in the browser tab title\n */\nconst tabTitle = {\n    id: '@jupyter-notebook/application-extension:tab-title',\n    description: 'A plugin to display the document title in the browser tab title.',\n    autoStart: true,\n    requires: [INotebookShell],\n    activate: (app, shell) => {\n        const setTabTitle = () => {\n            const current = shell.currentWidget;\n            if (current instanceof ConsolePanel) {\n                const update = () => {\n                    const title = current.sessionContext.path || current.sessionContext.name;\n                    const basename = PathExt.basename(title);\n                    // Strip the \".ipynb\" suffix from filenames for display in tab titles.\n                    document.title = basename.replace(STRIP_IPYNB, '');\n                };\n                current.sessionContext.sessionChanged.connect(update);\n                update();\n                return;\n            }\n            else if (current instanceof DocumentWidget) {\n                const update = () => {\n                    const basename = PathExt.basename(current.context.path);\n                    document.title = basename.replace(STRIP_IPYNB, '');\n                };\n                current.context.pathChanged.connect(update);\n                update();\n            }\n        };\n        shell.currentChanged.connect(setTabTitle);\n        setTabTitle();\n    },\n};\n/**\n * A plugin to display and rename the title of a file\n */\nconst title = {\n    id: '@jupyter-notebook/application-extension:title',\n    description: 'A plugin to display and rename the title of a file.',\n    autoStart: true,\n    requires: [INotebookShell, ITranslator],\n    optional: [IDocumentManager, IRouter, IToolbarWidgetRegistry],\n    activate: (app, shell, translator, docManager, router, toolbarRegistry) => {\n        const { commands } = app;\n        const trans = translator.load('notebook');\n        const node = document.createElement('div');\n        if (toolbarRegistry) {\n            toolbarRegistry.addFactory('TopBar', 'widgetTitle', (toolbar) => {\n                const widget = new Widget({ node });\n                widget.id = 'jp-title';\n                return widget;\n            });\n        }\n        const addTitle = async () => {\n            const current = shell.currentWidget;\n            if (!current || !(current instanceof DocumentWidget)) {\n                return;\n            }\n            if (node.children.length > 0) {\n                return;\n            }\n            const h = document.createElement('h1');\n            h.textContent = current.title.label.replace(STRIP_IPYNB, '');\n            node.appendChild(h);\n            node.style.marginLeft = '10px';\n            if (!docManager) {\n                return;\n            }\n            const isEnabled = () => {\n                const { currentWidget } = shell;\n                return !!(currentWidget && docManager.contextForWidget(currentWidget));\n            };\n            commands.addCommand(CommandIDs.duplicate, {\n                label: () => trans.__('Duplicate'),\n                isEnabled,\n                execute: async () => {\n                    if (!isEnabled()) {\n                        return;\n                    }\n                    // Duplicate the file, and open the new file.\n                    const result = await docManager.duplicate(current.context.path);\n                    await commands.execute('docmanager:open', { path: result.path });\n                },\n            });\n            commands.addCommand(CommandIDs.rename, {\n                label: () => trans.__('Rename…'),\n                isEnabled,\n                execute: async () => {\n                    var _a;\n                    if (!isEnabled()) {\n                        return;\n                    }\n                    const result = await renameDialog(docManager, current.context);\n                    // activate the current widget to bring the focus\n                    if (current) {\n                        current.activate();\n                    }\n                    if (result === null) {\n                        return;\n                    }\n                    const newPath = current.context.path;\n                    const basename = PathExt.basename(newPath);\n                    h.textContent = basename.replace(STRIP_IPYNB, '');\n                    if (!router) {\n                        return;\n                    }\n                    const matches = (_a = router.current.path.match(TREE_PATTERN)) !== null && _a !== void 0 ? _a : [];\n                    const [, route, path] = matches;\n                    if (!route || !path) {\n                        return;\n                    }\n                    const encoded = encodeURIComponent(newPath);\n                    router.navigate(`/${route}/${encoded}`, {\n                        skipRouting: true,\n                    });\n                },\n            });\n            node.onclick = async () => {\n                void commands.execute(CommandIDs.rename);\n            };\n        };\n        shell.currentChanged.connect(addTitle);\n        void addTitle();\n    },\n};\n/**\n * Plugin to toggle the top header visibility.\n */\nconst topVisibility = {\n    id: '@jupyter-notebook/application-extension:top',\n    description: 'Plugin to toggle the top header visibility.',\n    requires: [INotebookShell, ITranslator],\n    optional: [ISettingRegistry, ICommandPalette],\n    activate: (app, notebookShell, translator, settingRegistry, palette) => {\n        const trans = translator.load('notebook');\n        const top = notebookShell.top;\n        const pluginId = topVisibility.id;\n        app.commands.addCommand(CommandIDs.toggleTop, {\n            label: trans.__('Show Header'),\n            execute: () => {\n                top.setHidden(top.isVisible);\n                if (settingRegistry) {\n                    void settingRegistry.set(pluginId, 'visible', top.isVisible ? 'yes' : 'no');\n                }\n            },\n            isToggled: () => top.isVisible,\n        });\n        let adjustToScreen = false;\n        if (settingRegistry) {\n            const loadSettings = settingRegistry.load(pluginId);\n            const updateSettings = (settings) => {\n                // 'visible' property from user preferences or default settings\n                let visible = settings.get('visible').composite;\n                if (settings.user.visible !== undefined) {\n                    visible = settings.user.visible;\n                }\n                top.setHidden(visible === 'no');\n                // adjust to screen from user preferences or default settings\n                adjustToScreen = visible === 'automatic';\n            };\n            Promise.all([loadSettings, app.restored])\n                .then(([settings]) => {\n                updateSettings(settings);\n                settings.changed.connect((settings) => {\n                    updateSettings(settings);\n                });\n            })\n                .catch((reason) => {\n                console.error(reason.message);\n            });\n        }\n        if (palette) {\n            palette.addItem({ command: CommandIDs.toggleTop, category: 'View' });\n        }\n        const onChanged = () => {\n            if (!adjustToScreen) {\n                return;\n            }\n            if (app.format === 'desktop') {\n                notebookShell.expandTop();\n            }\n            else {\n                notebookShell.collapseTop();\n            }\n        };\n        // listen on format change (mobile and desktop) to make the view more compact\n        app.formatChanged.connect(onChanged);\n    },\n    autoStart: true,\n};\n/**\n * Plugin to toggle the left or right side panel's visibility.\n */\nconst sidePanelVisibility = {\n    id: '@jupyter-notebook/application-extension:sidepanel',\n    description: 'Plugin to toggle the visibility of left or right side panel.',\n    requires: [INotebookShell, ITranslator],\n    optional: [IMainMenu, ICommandPalette],\n    autoStart: true,\n    activate: (app, notebookShell, translator, menu, palette) => {\n        const trans = translator.load('notebook');\n        /* Arguments for togglePanel command:\n         * side, left or right area\n         * title, widget title to show in the menu\n         * id, widget ID to activate in the side panel\n         */\n        app.commands.addCommand(CommandIDs.togglePanel, {\n            label: (args) => args['title'],\n            caption: (args) => {\n                // We do not substitute the parameter into the string because the parameter is not\n                // localized (e.g., it is always 'left') even though the string is localized.\n                if (args['side'] === 'left') {\n                    return trans.__('Show %1 in the left sidebar', args['title']);\n                }\n                else if (args['side'] === 'right') {\n                    return trans.__('Show %1 in the right sidebar', args['title']);\n                }\n                return trans.__('Show %1 in the sidebar', args['title']);\n            },\n            execute: (args) => {\n                var _a, _b;\n                switch (args['side']) {\n                    case 'left':\n                        if (notebookShell.leftCollapsed) {\n                            notebookShell.expandLeft(args.id);\n                        }\n                        else if (((_a = notebookShell.leftHandler.currentWidget) === null || _a === void 0 ? void 0 : _a.id) !== args.id) {\n                            notebookShell.expandLeft(args.id);\n                        }\n                        else {\n                            notebookShell.collapseLeft();\n                            if (notebookShell.currentWidget) {\n                                notebookShell.activateById(notebookShell.currentWidget.id);\n                            }\n                        }\n                        break;\n                    case 'right':\n                        if (notebookShell.rightCollapsed) {\n                            notebookShell.expandRight(args.id);\n                        }\n                        else if (((_b = notebookShell.rightHandler.currentWidget) === null || _b === void 0 ? void 0 : _b.id) !== args.id) {\n                            notebookShell.expandRight(args.id);\n                        }\n                        else {\n                            notebookShell.collapseRight();\n                            if (notebookShell.currentWidget) {\n                                notebookShell.activateById(notebookShell.currentWidget.id);\n                            }\n                        }\n                        break;\n                }\n            },\n            isToggled: (args) => {\n                switch (args['side']) {\n                    case 'left': {\n                        if (notebookShell.leftCollapsed) {\n                            return false;\n                        }\n                        const currentWidget = notebookShell.leftHandler.currentWidget;\n                        if (!currentWidget) {\n                            return false;\n                        }\n                        return currentWidget.id === args['id'];\n                    }\n                    case 'right': {\n                        if (notebookShell.rightCollapsed) {\n                            return false;\n                        }\n                        const currentWidget = notebookShell.rightHandler.currentWidget;\n                        if (!currentWidget) {\n                            return false;\n                        }\n                        return currentWidget.id === args['id'];\n                    }\n                }\n                return false;\n            },\n        });\n        const sidePanelMenu = {\n            left: null,\n            right: null,\n        };\n        /**\n         * The function which adds entries to the View menu for each widget of a side panel.\n         *\n         * @param area - 'left' or 'right', the area of the side panel.\n         * @param entryLabel - the name of the main entry in the View menu for that side panel.\n         * @returns - The disposable menu added to the View menu or null.\n         */\n        const updateMenu = (area, entryLabel) => {\n            var _a;\n            if (menu === null) {\n                return null;\n            }\n            // Remove the previous menu entry for this side panel.\n            (_a = sidePanelMenu[area]) === null || _a === void 0 ? void 0 : _a.dispose();\n            // Creates a new menu entry and populates it with side panel widgets.\n            const newMenu = new Menu({ commands: app.commands });\n            newMenu.title.label = entryLabel;\n            const widgets = notebookShell.widgets(area);\n            let menuToAdd = false;\n            for (const widget of widgets) {\n                newMenu.addItem({\n                    command: CommandIDs.togglePanel,\n                    args: {\n                        side: area,\n                        title: `Show ${widget.title.caption}`,\n                        id: widget.id,\n                    },\n                });\n                menuToAdd = true;\n            }\n            // If there are widgets, add the menu to the main menu entry.\n            if (menuToAdd) {\n                sidePanelMenu[area] = menu.viewMenu.addItem({\n                    type: 'submenu',\n                    submenu: newMenu,\n                });\n            }\n        };\n        app.restored.then(() => {\n            // Create menu entries for the left and right panel.\n            if (menu) {\n                const getSidePanelLabel = (area) => {\n                    if (area === 'left') {\n                        return trans.__('Left Sidebar');\n                    }\n                    else {\n                        return trans.__('Right Sidebar');\n                    }\n                };\n                const leftArea = notebookShell.leftHandler.area;\n                const leftLabel = getSidePanelLabel(leftArea);\n                updateMenu(leftArea, leftLabel);\n                const rightArea = notebookShell.rightHandler.area;\n                const rightLabel = getSidePanelLabel(rightArea);\n                updateMenu(rightArea, rightLabel);\n                const handleSidePanelChange = (sidePanel, widget) => {\n                    const label = getSidePanelLabel(sidePanel.area);\n                    updateMenu(sidePanel.area, label);\n                };\n                notebookShell.leftHandler.widgetAdded.connect(handleSidePanelChange);\n                notebookShell.leftHandler.widgetRemoved.connect(handleSidePanelChange);\n                notebookShell.rightHandler.widgetAdded.connect(handleSidePanelChange);\n                notebookShell.rightHandler.widgetRemoved.connect(handleSidePanelChange);\n            }\n            // Add palette entries for side panels.\n            if (palette) {\n                const sidePanelPalette = new SidePanelPalette({\n                    commandPalette: palette,\n                    command: CommandIDs.togglePanel,\n                });\n                notebookShell.leftHandler.widgets.forEach((widget) => {\n                    sidePanelPalette.addItem(widget, notebookShell.leftHandler.area);\n                });\n                notebookShell.rightHandler.widgets.forEach((widget) => {\n                    sidePanelPalette.addItem(widget, notebookShell.rightHandler.area);\n                });\n                // Update menu and palette when widgets are added or removed from side panels.\n                notebookShell.leftHandler.widgetAdded.connect((sidePanel, widget) => {\n                    sidePanelPalette.addItem(widget, sidePanel.area);\n                });\n                notebookShell.leftHandler.widgetRemoved.connect((sidePanel, widget) => {\n                    sidePanelPalette.removeItem(widget, sidePanel.area);\n                });\n                notebookShell.rightHandler.widgetAdded.connect((sidePanel, widget) => {\n                    sidePanelPalette.addItem(widget, sidePanel.area);\n                });\n                notebookShell.rightHandler.widgetRemoved.connect((sidePanel, widget) => {\n                    sidePanelPalette.removeItem(widget, sidePanel.area);\n                });\n            }\n        });\n    },\n};\n/**\n * The default tree route resolver plugin.\n */\nconst tree = {\n    id: '@jupyter-notebook/application-extension:tree-resolver',\n    description: 'The default tree route resolver plugin.',\n    autoStart: true,\n    requires: [IRouter],\n    provides: JupyterFrontEnd.ITreeResolver,\n    activate: (app, router) => {\n        const { commands } = app;\n        const set = new DisposableSet();\n        const delegate = new PromiseDelegate();\n        const treePattern = new RegExp('/(/tree/.*)?');\n        set.add(commands.addCommand(CommandIDs.resolveTree, {\n            execute: (async (args) => {\n                var _a;\n                if (set.isDisposed) {\n                    return;\n                }\n                const query = URLExt.queryStringToObject((_a = args.search) !== null && _a !== void 0 ? _a : '');\n                const browser = query['file-browser-path'] || '';\n                // Remove the file browser path from the query string.\n                delete query['file-browser-path'];\n                // Clean up artifacts immediately upon routing.\n                set.dispose();\n                delegate.resolve({ browser, file: PageConfig.getOption('treePath') });\n            }),\n        }));\n        set.add(router.register({ command: CommandIDs.resolveTree, pattern: treePattern }));\n        // If a route is handled by the router without the tree command being\n        // invoked, resolve to `null` and clean up artifacts.\n        const listener = () => {\n            if (set.isDisposed) {\n                return;\n            }\n            set.dispose();\n            delegate.resolve(null);\n        };\n        router.routed.connect(listener);\n        set.add(new DisposableDelegate(() => {\n            router.routed.disconnect(listener);\n        }));\n        return { paths: delegate.promise };\n    },\n};\n/**\n * Plugin to update tree path.\n */\nconst treePathUpdater = {\n    id: '@jupyter-notebook/application-extension:tree-updater',\n    description: 'Plugin to update tree path.',\n    requires: [IRouter],\n    provides: ITreePathUpdater,\n    activate: (app, router) => {\n        function updateTreePath(treePath) {\n            if (treePath !== PageConfig.getOption('treePath')) {\n                const path = URLExt.join(PageConfig.getOption('baseUrl') || '/', 'tree', URLExt.encodeParts(treePath));\n                router.navigate(path, { skipRouting: true });\n                // Persist the new tree path to PageConfig as it is used elsewhere at runtime.\n                PageConfig.setOption('treePath', treePath);\n            }\n        }\n        return updateTreePath;\n    },\n    autoStart: true,\n};\n/**\n * Translator plugin\n */\nconst translator = {\n    id: '@jupyter-notebook/application-extension:translator',\n    description: 'Translator plugin',\n    requires: [INotebookShell, ITranslator],\n    autoStart: true,\n    activate: (app, notebookShell, translator) => {\n        notebookShell.translator = translator;\n    },\n};\n/**\n * Zen mode plugin\n */\nconst zen = {\n    id: '@jupyter-notebook/application-extension:zen',\n    description: 'Zen mode plugin.',\n    autoStart: true,\n    requires: [ITranslator],\n    optional: [ICommandPalette, INotebookShell],\n    activate: (app, translator, palette, notebookShell) => {\n        const { commands } = app;\n        const elem = document.documentElement;\n        const trans = translator.load('notebook');\n        const toggleOn = () => {\n            notebookShell === null || notebookShell === void 0 ? void 0 : notebookShell.collapseTop();\n            notebookShell === null || notebookShell === void 0 ? void 0 : notebookShell.menu.setHidden(true);\n            zenModeEnabled = true;\n        };\n        const toggleOff = () => {\n            notebookShell === null || notebookShell === void 0 ? void 0 : notebookShell.expandTop();\n            notebookShell === null || notebookShell === void 0 ? void 0 : notebookShell.menu.setHidden(false);\n            zenModeEnabled = false;\n        };\n        let zenModeEnabled = false;\n        commands.addCommand(CommandIDs.toggleZen, {\n            label: trans.__('Toggle Zen Mode'),\n            execute: () => {\n                if (!zenModeEnabled) {\n                    elem.requestFullscreen();\n                    toggleOn();\n                }\n                else {\n                    document.exitFullscreen();\n                    toggleOff();\n                }\n            },\n        });\n        document.addEventListener('fullscreenchange', () => {\n            if (!document.fullscreenElement) {\n                toggleOff();\n            }\n        });\n        if (palette) {\n            palette.addItem({ command: CommandIDs.toggleZen, category: 'Mode' });\n        }\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [\n    dirty,\n    info,\n    logo,\n    menus,\n    menuSpacer,\n    opener,\n    pages,\n    pathOpener,\n    paths,\n    rendermime,\n    shell,\n    sidePanelVisibility,\n    splash,\n    status,\n    tabTitle,\n    title,\n    topVisibility,\n    tree,\n    treePathUpdater,\n    translator,\n    zen,\n];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}