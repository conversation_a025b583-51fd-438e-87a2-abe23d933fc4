{"title": "File Browser Widget", "description": "File Browser widget settings.", "jupyter.lab.toolbars": {"FileBrowser": [{"name": "spacer", "type": "spacer", "rank": 900}, {"name": "toggle-file-filter", "label": "", "command": "filebrowser:toggle-file-filter", "rank": 990}, {"name": "new-dropdown", "rank": 1000}, {"name": "uploader", "rank": 1010}, {"name": "refresh", "command": "filebrowser:refresh", "rank": 1020}]}, "jupyter.lab.transform": true, "properties": {"toolbar": {"title": "File browser toolbar items", "description": "Note: To disable a toolbar item,\ncopy it to User Preferences and add the\n\"disabled\" key. The following example will disable the uploader button:\n{\n  \"toolbar\": [\n    {\n      \"name\": \"uploader\",\n      \"disabled\": true\n    }\n  ]\n}\n\nToolbar description:", "items": {"$ref": "#/definitions/toolbarItem"}, "type": "array", "default": []}}, "additionalProperties": false, "type": "object", "definitions": {"toolbarItem": {"properties": {"name": {"title": "Unique name", "type": "string"}, "args": {"title": "Command arguments", "type": "object"}, "command": {"title": "Command id", "type": "string", "default": ""}, "disabled": {"title": "Whether the item is ignored or not", "type": "boolean", "default": false}, "icon": {"title": "Item icon id", "description": "If defined, it will override the command icon", "type": "string"}, "label": {"title": "Item label", "description": "If defined, it will override the command label", "type": "string"}, "type": {"title": "Item type", "type": "string", "enum": ["command", "spacer"]}, "rank": {"title": "Item rank", "type": "number", "minimum": 0, "default": 50}}, "required": ["name"], "additionalProperties": false, "type": "object"}}}