# ChemProp 模型训练器 - 优化版本

## 概述

这是一个针对 ChemProp 2.2.0 优化的 Lipophilicity 预测模型训练器。该工具提供了完整的模型训练、超参数搜索、结果分析和预测功能。

## 主要特性

### ✨ 核心功能
- **兼容 ChemProp 2.2.0**: 完全适配最新版本的 ChemProp API
- **智能超参数搜索**: 提供快速模式和完整模式两种搜索策略
- **自动结果解析**: 从训练输出中智能提取性能指标
- **可视化分析**: 生成详细的性能分析图表和报告
- **灵活预测**: 支持单分子和批量分子预测

### 🚀 优化改进
- **错误处理**: 完善的异常处理和日志记录
- **参数映射**: 自动处理新旧版本参数名称差异
- **结果验证**: 多种方式验证训练成功性
- **中文支持**: 完整的中文界面和日志输出
- **命令行接口**: 灵活的命令行参数支持

## 使用方法

### 基本用法

```bash
# 激活环境
chemprop_env\Scripts\activate

# 快速模式训练（推荐用于测试）
python scripts\model_trainer.py --quick-mode

# 完整模式训练
python scripts\model_trainer.py

# 自定义输出目录
python scripts\model_trainer.py --output-dir my_models

# 自定义测试分子
python scripts\model_trainer.py --test-smiles "CCO" "c1ccccc1" "CC(C)O"
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--data-file` | 训练数据文件路径 | `official_lipo_data.csv` |
| `--output-dir` | 模型输出目录 | `lipo_models` |
| `--quick-mode` | 使用快速模式（较少配置） | `False` |
| `--test-smiles` | 用于测试的SMILES字符串列表 | 默认测试分子 |
| `--log-level` | 日志级别 (DEBUG/INFO/WARNING/ERROR) | `INFO` |

### 数据格式要求

训练数据文件应为 CSV 格式，包含以下列：
- `smiles`: 分子的 SMILES 字符串
- `lipo`: Lipophilicity 值（logP）

示例：
```csv
smiles,lipo
CCO,-0.31
c1ccccc1,2.13
CC(C)O,0.05
```

## 超参数配置

### 快速模式配置
- **quick_baseline**: 基础配置，训练时间短
- **quick_optimized**: 优化配置，性能更好

### 完整模式配置
- **baseline**: 标准基线模型
- **deeper_model**: 更深的网络结构
- **wider_model**: 更宽的隐藏层
- **high_dropout**: 高 dropout 率
- **optimized_lr**: 优化学习率
- **ensemble_model**: 集成学习模型
- **deep_wide**: 深度宽网络模型

## 输出文件

训练完成后，会在输出目录生成以下文件：

### 📊 结果文件
- `training_results.csv`: 详细训练结果
- `performance_report.txt`: 性能分析报告
- `training_analysis.png`: 可视化分析图表
- `lipo_predictions.csv`: 测试分子预测结果

### 🤖 模型文件
每个配置会生成独立的模型目录，包含：
- `model_0/best.pt`: 最佳模型权重
- `config.toml`: 训练配置
- `checkpoints/`: 训练检查点

## 性能指标

### 评估指标
- **MAE (Mean Absolute Error)**: 平均绝对误差
- **RMSE (Root Mean Square Error)**: 均方根误差
- **训练时间**: 模型训练耗时

### 典型性能
在 100 个分子的 lipophilicity 数据集上：
- 快速模式: MAE ≈ 1.75, 训练时间 < 30s
- 完整模式: MAE ≈ 1.75, 训练时间 < 150s

## 故障排除

### 常见问题

1. **训练失败**: 检查 ChemProp 环境是否正确安装
2. **参数错误**: 确保使用 ChemProp 2.2.0 兼容的参数
3. **内存不足**: 减少 batch_size 或使用快速模式
4. **中文显示问题**: 安装中文字体支持

### 调试技巧

```bash
# 启用详细日志
python scripts\model_trainer.py --log-level DEBUG

# 检查训练日志
cat chemprop_training.log

# 验证数据格式
python -c "import pandas as pd; print(pd.read_csv('official_lipo_data.csv').head())"
```

## 扩展功能

### 自定义配置
可以通过修改 `_get_full_configs()` 方法添加新的超参数配置：

```python
{
    'name': 'custom_config',
    'params': {
        'depth': 5,
        'hidden-size': 512,
        'dropout': 0.15,
        'epochs': 60,
        'batch-size': 64,
        'learning-rate': 0.0002
    }
}
```

### 集成其他数据集
只需确保数据格式符合要求，即可用于其他分子性质预测任务。

## 技术细节

### 兼容性处理
- 自动映射新旧版本参数名称
- 智能检测模型文件位置
- 从训练输出提取性能指标

### 性能优化
- 并行数据加载
- 内存高效的数据处理
- 智能超时处理

## 更新日志

### v2.0 (当前版本)
- ✅ 完全兼容 ChemProp 2.2.0
- ✅ 新增智能结果解析
- ✅ 改进错误处理和日志
- ✅ 优化可视化效果
- ✅ 添加命令行接口

### v1.0 (原始版本)
- 基础训练功能
- 简单超参数搜索
- 基本结果分析

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！

## 许可证

MIT License
