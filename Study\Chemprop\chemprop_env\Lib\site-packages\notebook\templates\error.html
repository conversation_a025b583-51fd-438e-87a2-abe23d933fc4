<!doctype html><html><head><meta charset="utf-8"><title>{% block title %}{{page_title | e}}{% endblock %}</title>{% block favicon %}<link rel="shortcut icon" type="image/x-icon" href="/static/favicons/favicon.ico">{% endblock %}<script defer="defer" src="{{page_config.fullStaticUrl}}/main.58cb79c84cf80ba7b6ed.js?v=58cb79c84cf80ba7b6ed"></script></head><body class="jp-ThemedContainer">{% block stylesheet %}<style>/* disable initial hide */
div#header, div#site {
    display: block;
}</style>{% endblock %} {% block site %}<div class="error">{% block h1_error %}<h1>{{status_code | e}} : {{status_message | e}}</h1>{% endblock h1_error %} {% block error_detail %} {% if message %}<p>The error was:</p><div class="traceback-wrapper"><pre class="traceback">{{message | e}}</pre></div>{% endif %} {% endblock %}{% endblock %} {% block script %}<script>window.onload = function () {
  var tb = document.getElementsByClassName('traceback')[0];
  tb.scrollTop = tb.scrollHeight;
  {% if message %}
  console.error("{{message | e}}")
  {% endif %}
};</script>{% endblock script %}</div></body></html>