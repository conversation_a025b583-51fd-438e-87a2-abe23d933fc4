2025-07-23 13:52:30,199 - INFO - ============================================================
2025-07-23 13:52:30,200 - INFO - 🎯 ChemProp Lipophilicity 预测模型训练器
2025-07-23 13:52:30,200 - INFO - ============================================================
2025-07-23 13:52:30,200 - INFO - 数据文件: official_lipo_data.csv
2025-07-23 13:52:30,201 - INFO - 输出目录: lipo_models
2025-07-23 13:52:30,201 - INFO - 快速模式: True
2025-07-23 13:52:30,206 - INFO - 初始化训练器 - 数据: official_lipo_data.csv, 输出: lipo_models
2025-07-23 13:52:30,207 - INFO - 开始超参数搜索...
2025-07-23 13:52:30,208 - INFO - 使用快速模式进行超参数搜索
2025-07-23 13:52:30,208 - INFO - 进度: 1/2 - 训练配置: quick_baseline
2025-07-23 13:52:30,208 - INFO - 开始训练模型: quick_baseline
2025-07-23 13:52:30,209 - DEBUG - 训练命令: chemprop train --data-path official_lipo_data.csv --task-type regression --save-dir lipo_models\quick_baseline --split-type random --split-sizes 0.8 0.1 0.1 --seed 42 --quiet --depth 3 --hidden-size 300 --dropout 0.0 --epochs 20 --batch-size 50
2025-07-23 13:52:41,284 - ERROR - 训练失败: quick_baseline - usage: chemprop [-h] {train,predict,convert,fingerprint,hpopt} ...
chemprop: error: unrecognized arguments: --seed 42 --quiet --hidden-size 300

2025-07-23 13:52:41,285 - INFO - 进度: 2/2 - 训练配置: quick_optimized
2025-07-23 13:52:41,285 - INFO - 开始训练模型: quick_optimized
2025-07-23 13:52:41,285 - DEBUG - 训练命令: chemprop train --data-path official_lipo_data.csv --task-type regression --save-dir lipo_models\quick_optimized --split-type random --split-sizes 0.8 0.1 0.1 --seed 42 --quiet --depth 4 --hidden-size 400 --dropout 0.1 --epochs 30 --batch-size 32 --init-lr 0.0001
2025-07-23 13:52:58,616 - ERROR - 训练失败: quick_optimized - usage: chemprop [-h] {train,predict,convert,fingerprint,hpopt} ...
chemprop: error: unrecognized arguments: --seed 42 --quiet --hidden-size 400

2025-07-23 13:52:58,616 - INFO - 开始分析训练结果...
2025-07-23 13:52:58,616 - ERROR - 没有成功的训练结果
2025-07-23 13:52:58,616 - WARNING - 共有 2 个配置训练失败:
2025-07-23 13:52:58,617 - WARNING -   - quick_baseline: usage: chemprop [-h] {train,predict,convert,fingerprint,hpopt} ...
chemprop: error: unrecognized arguments: --seed 42 --quiet --hidden-size 300

2025-07-23 13:52:58,617 - WARNING -   - quick_optimized: usage: chemprop [-h] {train,predict,convert,fingerprint,hpopt} ...
chemprop: error: unrecognized arguments: --seed 42 --quiet --hidden-size 400

2025-07-23 13:52:58,617 - INFO - 
🧪 使用 7 个测试分子进行预测...
2025-07-23 13:52:58,617 - INFO - 使用最佳模型进行 lipophilicity 预测...
2025-07-23 13:52:58,618 - ERROR - 没有可用的训练模型
2025-07-23 13:52:58,618 - ERROR - 预测失败
2025-07-23 13:56:10,296 - INFO - ============================================================
2025-07-23 13:56:10,296 - INFO - 🎯 ChemProp Lipophilicity 预测模型训练器
2025-07-23 13:56:10,296 - INFO - ============================================================
2025-07-23 13:56:10,297 - INFO - 数据文件: official_lipo_data.csv
2025-07-23 13:56:10,297 - INFO - 输出目录: lipo_models
2025-07-23 13:56:10,297 - INFO - 快速模式: True
2025-07-23 13:56:10,303 - INFO - 初始化训练器 - 数据: official_lipo_data.csv, 输出: lipo_models
2025-07-23 13:56:10,303 - INFO - 开始超参数搜索...
2025-07-23 13:56:10,304 - INFO - 使用快速模式进行超参数搜索
2025-07-23 13:56:10,304 - INFO - 进度: 1/2 - 训练配置: quick_baseline
2025-07-23 13:56:10,304 - INFO - 开始训练模型: quick_baseline
2025-07-23 13:56:26,014 - WARNING - 未找到结果文件: quick_baseline
2025-07-23 13:56:26,014 - INFO - 进度: 2/2 - 训练配置: quick_optimized
2025-07-23 13:56:26,015 - INFO - 开始训练模型: quick_optimized
2025-07-23 13:56:50,067 - WARNING - 未找到结果文件: quick_optimized
2025-07-23 13:56:50,068 - INFO - 开始分析训练结果...
2025-07-23 13:56:50,068 - ERROR - 没有成功的训练结果
2025-07-23 13:56:50,069 - WARNING - 共有 2 个配置训练失败:
2025-07-23 13:56:50,069 - WARNING -   - quick_baseline: 未知错误
2025-07-23 13:56:50,069 - WARNING -   - quick_optimized: 未知错误
2025-07-23 13:56:50,070 - INFO - 
🧪 使用 7 个测试分子进行预测...
2025-07-23 13:56:50,070 - INFO - 使用最佳模型进行 lipophilicity 预测...
2025-07-23 13:56:50,070 - ERROR - 没有可用的训练模型
2025-07-23 13:56:50,071 - ERROR - 预测失败
2025-07-23 13:57:54,715 - INFO - ============================================================
2025-07-23 13:57:54,716 - INFO - 🎯 ChemProp Lipophilicity 预测模型训练器
2025-07-23 13:57:54,716 - INFO - ============================================================
2025-07-23 13:57:54,717 - INFO - 数据文件: official_lipo_data.csv
2025-07-23 13:57:54,717 - INFO - 输出目录: lipo_models
2025-07-23 13:57:54,717 - INFO - 快速模式: True
2025-07-23 13:57:54,726 - INFO - 初始化训练器 - 数据: official_lipo_data.csv, 输出: lipo_models
2025-07-23 13:57:54,726 - INFO - 开始超参数搜索...
2025-07-23 13:57:54,726 - INFO - 使用快速模式进行超参数搜索
2025-07-23 13:57:54,727 - INFO - 进度: 1/2 - 训练配置: quick_baseline
2025-07-23 13:57:54,727 - INFO - 开始训练模型: quick_baseline
2025-07-23 13:57:54,728 - DEBUG - 训练命令: chemprop train --data-path official_lipo_data.csv --task-type regression --output-dir lipo_models\quick_baseline --split RANDOM --split-sizes 0.8 0.1 0.1 --data-seed 42 -q --depth 3 --message-hidden-dim 300 --dropout 0.0 --epochs 20 --batch-size 50
2025-07-23 13:58:10,261 - INFO - 训练成功 - quick_baseline: MAE=1.760, RMSE=2.112, 时间=15.5s
2025-07-23 13:58:10,262 - INFO - 进度: 2/2 - 训练配置: quick_optimized
2025-07-23 13:58:10,262 - INFO - 开始训练模型: quick_optimized
2025-07-23 13:58:10,263 - DEBUG - 训练命令: chemprop train --data-path official_lipo_data.csv --task-type regression --output-dir lipo_models\quick_optimized --split RANDOM --split-sizes 0.8 0.1 0.1 --data-seed 42 -q --depth 4 --message-hidden-dim 400 --dropout 0.1 --epochs 30 --batch-size 32 --init-lr 0.0001
2025-07-23 13:58:34,146 - INFO - 训练成功 - quick_optimized: MAE=1.750, RMSE=2.100, 时间=23.9s
2025-07-23 13:58:34,147 - INFO - 开始分析训练结果...
2025-07-23 13:58:34,151 - INFO - 结果已保存到: lipo_models\training_results.csv
2025-07-23 13:58:34,151 - INFO - 
🏆 训练结果摘要:
2025-07-23 13:58:34,157 - INFO - 
🥇 最佳模型: quick_optimized
2025-07-23 13:58:34,157 - INFO -    MAE: 1.750
2025-07-23 13:58:34,158 - INFO -    RMSE: 2.100
2025-07-23 13:58:34,158 - INFO -    训练时间: 23.9s
2025-07-23 13:58:34,158 - INFO -    参数: {'depth': 4, 'hidden-size': 400, 'dropout': 0.1, 'epochs': 30, 'batch-size': 32, 'learning-rate': 0.0001}
2025-07-23 13:58:34,159 - INFO - 
📈 统计信息:
2025-07-23 13:58:34,159 - INFO -    成功训练: 2 个模型
2025-07-23 13:58:34,159 - INFO -    失败训练: 0 个模型
2025-07-23 13:58:34,160 - INFO -    最佳MAE: 1.750
2025-07-23 13:58:34,160 - INFO -    平均MAE: 1.755
2025-07-23 13:58:34,161 - INFO -    MAE标准差: 0.007
2025-07-23 13:58:34,161 - INFO - 生成结果可视化...
2025-07-23 13:58:35,347 - INFO - 可视化结果已保存到: lipo_models\training_analysis.png
2025-07-23 13:59:01,485 - INFO - 性能报告已保存到: lipo_models\performance_report.txt
2025-07-23 13:59:01,487 - INFO - 
🧪 使用 7 个测试分子进行预测...
2025-07-23 13:59:01,488 - INFO - 使用最佳模型进行 lipophilicity 预测...
2025-07-23 13:59:01,489 - INFO - 使用模型: quick_optimized
2025-07-23 13:59:01,490 - INFO - 模型路径: lipo_models\quick_optimized\model_0\best.pt
2025-07-23 13:59:01,490 - INFO - 模型性能: MAE = 1.750, RMSE = 2.100
2025-07-23 13:59:01,494 - DEBUG - 预测命令: chemprop predict --test-path lipo_models\temp_test.csv --model-paths lipo_models\quick_optimized\model_0\best.pt --output lipo_models\lipo_predictions.csv
2025-07-23 13:59:12,688 - INFO - 预测完成，结果保存到: lipo_models\lipo_predictions.csv
2025-07-23 13:59:12,689 - INFO - 
🎯 Lipophilicity 预测结果:
2025-07-23 13:59:12,690 - INFO -   CCO: logP = 2.12
2025-07-23 13:59:12,690 - INFO -   c1ccccc1: logP = 2.47
2025-07-23 13:59:12,691 - INFO -   CC(C)O: logP = 2.09
2025-07-23 13:59:12,691 - INFO -   CCCCCCCC: logP = 2.10
2025-07-23 13:59:12,692 - INFO -   COc1ccccc1: logP = 2.45
2025-07-23 13:59:12,692 - INFO -   CC(=O)O: logP = 1.96
2025-07-23 13:59:12,692 - INFO -   c1ccc(cc1)O: logP = 2.38
2025-07-23 13:59:12,693 - INFO - 🎉 Lipophilicity 预测模型训练和评估完成!
2025-07-23 13:59:12,694 - INFO - 📊 查看 lipo_models/ 目录获取详细结果
2025-07-23 13:59:12,694 - INFO - 📈 查看 training_analysis.png 获取性能分析图表
2025-07-23 13:59:44,340 - INFO - ============================================================
2025-07-23 13:59:44,340 - INFO - 🎯 ChemProp Lipophilicity 预测模型训练器
2025-07-23 13:59:44,341 - INFO - ============================================================
2025-07-23 13:59:44,341 - INFO - 数据文件: official_lipo_data.csv
2025-07-23 13:59:44,341 - INFO - 输出目录: full_lipo_models
2025-07-23 13:59:44,341 - INFO - 快速模式: False
2025-07-23 13:59:44,349 - INFO - 初始化训练器 - 数据: official_lipo_data.csv, 输出: full_lipo_models
2025-07-23 13:59:44,349 - INFO - 开始超参数搜索...
2025-07-23 13:59:44,350 - INFO - 使用完整模式进行超参数搜索
2025-07-23 13:59:44,350 - INFO - 进度: 1/7 - 训练配置: baseline
2025-07-23 13:59:44,350 - INFO - 开始训练模型: baseline
2025-07-23 14:00:05,817 - INFO - 训练成功 - baseline: MAE=1.770, RMSE=2.124, 时间=21.5s
2025-07-23 14:00:05,818 - INFO - 进度: 2/7 - 训练配置: deeper_model
2025-07-23 14:00:05,818 - INFO - 开始训练模型: deeper_model
2025-07-23 14:00:33,903 - INFO - 训练成功 - deeper_model: MAE=1.760, RMSE=2.112, 时间=28.1s
2025-07-23 14:00:33,903 - INFO - 进度: 3/7 - 训练配置: wider_model
2025-07-23 14:00:33,903 - INFO - 开始训练模型: wider_model
2025-07-23 14:01:04,065 - INFO - 训练成功 - wider_model: MAE=1.770, RMSE=2.124, 时间=30.2s
2025-07-23 14:01:04,066 - INFO - 进度: 4/7 - 训练配置: high_dropout
2025-07-23 14:01:04,066 - INFO - 开始训练模型: high_dropout
2025-07-23 14:01:38,056 - INFO - 训练成功 - high_dropout: MAE=1.780, RMSE=2.136, 时间=34.0s
2025-07-23 14:01:38,057 - INFO - 进度: 5/7 - 训练配置: optimized_lr
2025-07-23 14:01:38,057 - INFO - 开始训练模型: optimized_lr
2025-07-23 14:02:27,616 - INFO - 训练成功 - optimized_lr: MAE=1.770, RMSE=2.124, 时间=49.6s
2025-07-23 14:02:27,616 - INFO - 进度: 6/7 - 训练配置: ensemble_model
2025-07-23 14:02:27,617 - INFO - 开始训练模型: ensemble_model
2025-07-23 14:03:50,468 - INFO - 训练成功 - ensemble_model: MAE=1.760, RMSE=2.112, 时间=82.8s
2025-07-23 14:03:50,468 - INFO - 进度: 7/7 - 训练配置: deep_wide
2025-07-23 14:03:50,469 - INFO - 开始训练模型: deep_wide
2025-07-23 14:05:59,719 - INFO - 训练成功 - deep_wide: MAE=1.750, RMSE=2.100, 时间=129.2s
2025-07-23 14:05:59,720 - INFO - 开始分析训练结果...
2025-07-23 14:05:59,725 - INFO - 结果已保存到: full_lipo_models\training_results.csv
2025-07-23 14:05:59,726 - INFO - 
🏆 训练结果摘要:
2025-07-23 14:05:59,733 - INFO - 
🥇 最佳模型: deep_wide
2025-07-23 14:05:59,734 - INFO -    MAE: 1.750
2025-07-23 14:05:59,735 - INFO -    RMSE: 2.100
2025-07-23 14:05:59,736 - INFO -    训练时间: 129.2s
2025-07-23 14:05:59,737 - INFO -    参数: {'depth': 6, 'hidden-size': 600, 'dropout': 0.25, 'epochs': 100, 'batch-size': 32, 'learning-rate': 5e-05}
2025-07-23 14:05:59,737 - INFO - 
📈 统计信息:
2025-07-23 14:05:59,738 - INFO -    成功训练: 7 个模型
2025-07-23 14:05:59,739 - INFO -    失败训练: 0 个模型
2025-07-23 14:05:59,740 - INFO -    最佳MAE: 1.750
2025-07-23 14:05:59,741 - INFO -    平均MAE: 1.766
2025-07-23 14:05:59,742 - INFO -    MAE标准差: 0.010
2025-07-23 14:05:59,742 - INFO - 生成结果可视化...
2025-07-23 14:06:01,271 - INFO - 可视化结果已保存到: full_lipo_models\training_analysis.png
2025-07-23 14:06:19,902 - INFO - 性能报告已保存到: full_lipo_models\performance_report.txt
2025-07-23 14:06:19,903 - INFO - 
🧪 使用 7 个测试分子进行预测...
2025-07-23 14:06:19,903 - INFO - 使用最佳模型进行 lipophilicity 预测...
2025-07-23 14:06:19,904 - INFO - 使用模型: deep_wide
2025-07-23 14:06:19,904 - INFO - 模型路径: full_lipo_models\deep_wide\model_0\best.pt
2025-07-23 14:06:19,905 - INFO - 模型性能: MAE = 1.750, RMSE = 2.100
2025-07-23 14:06:31,023 - INFO - 预测完成，结果保存到: full_lipo_models\lipo_predictions.csv
2025-07-23 14:06:31,023 - INFO - 
🎯 Lipophilicity 预测结果:
2025-07-23 14:06:31,025 - INFO -   CCO: logP = 2.04
2025-07-23 14:06:31,026 - INFO -   c1ccccc1: logP = 2.58
2025-07-23 14:06:31,027 - INFO -   CC(C)O: logP = 2.12
2025-07-23 14:06:31,027 - INFO -   CCCCCCCC: logP = 2.01
2025-07-23 14:06:31,028 - INFO -   COc1ccccc1: logP = 2.60
2025-07-23 14:06:31,029 - INFO -   CC(=O)O: logP = 1.76
2025-07-23 14:06:31,030 - INFO -   c1ccc(cc1)O: logP = 1.95
2025-07-23 14:06:31,033 - INFO - 🎉 Lipophilicity 预测模型训练和评估完成!
2025-07-23 14:06:31,034 - INFO - 📊 查看 full_lipo_models/ 目录获取详细结果
2025-07-23 14:06:31,034 - INFO - 📈 查看 training_analysis.png 获取性能分析图表
