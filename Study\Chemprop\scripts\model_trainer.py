#!/usr/bin/env python3
"""
ChemProp 模型训练和评估框架
"""

import subprocess
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import json
import os
from pathlib import Path
import time

class ChemPropTrainer:
    """ChemProp 训练器"""
    
    def __init__(self, data_path, output_dir="chemprop_models"):
        """初始化"""
        self.data_path = data_path
        self.output_dir = output_dir
        self.results = []
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
    
    def train_model(self, config):
        """训练单个模型"""
        print(f"\n🚀 训练模型: {config['name']}")
        
        # 构建训练命令
        cmd = [
            "chemprop", "train",
            "--data-path", self.data_path,
            "--task-type", "regression",
            "--output-dir", os.path.join(self.output_dir, config['name']),
            "--split-type", "random",
            "--split-sizes", "0.8", "0.1", "0.1",
            "--data-seed", "42"
        ]
        
        # 添加配置参数
        for param, value in config['params'].items():
            cmd.extend([f"--{param}", str(value)])
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 运行训练
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # 记录结束时间
            end_time = time.time()
            training_time = end_time - start_time
            
            # 读取结果
            test_scores_file = os.path.join(self.output_dir, config['name'], "test_scores.csv")
            
            if os.path.exists(test_scores_file):
                scores = pd.read_csv(test_scores_file)
                mae = scores.iloc[0, 1]  # 第二列通常是 MAE
                rmse = scores.iloc[0, 2] if len(scores.columns) > 2 else np.sqrt(mae**2)  # 估算 RMSE
                
                result_dict = {
                    'name': config['name'],
                    'mae': mae,
                    'rmse': rmse,
                    'training_time': training_time,
                    'params': config['params'],
                    'status': 'success'
                }
                
                print(f"✅ 训练成功 - MAE: {mae:.3f}, RMSE: {rmse:.3f}, 时间: {training_time:.1f}s")
                
            else:
                result_dict = {
                    'name': config['name'],
                    'mae': None,
                    'rmse': None,
                    'training_time': training_time,
                    'params': config['params'],
                    'status': 'no_results'
                }
                print("⚠️ 训练完成但未找到结果文件")
            
        except subprocess.CalledProcessError as e:
            result_dict = {
                'name': config['name'],
                'mae': None,
                'rmse': None,
                'training_time': None,
                'params': config['params'],
                'status': 'failed',
                'error': e.stderr
            }
            print(f"❌ 训练失败: {e.stderr}")
        
        self.results.append(result_dict)
        return result_dict
    
    def hyperparameter_search(self):
        """超参数搜索 - 针对 lipophilicity 预测优化"""
        print("🔧 开始超参数搜索...")
        
        # 定义搜索配置 - 针对 lipophilicity 数据集优化
        configs = [
            {
                'name': 'baseline',
                'params': {
                    'depth': 3,
                    'hidden-size': 300,
                    'dropout': 0.0,
                    'epochs': 50,  # 增加训练轮数
                    'batch-size': 50
                }
            },
            {
                'name': 'deeper_model',
                'params': {
                    'depth': 5,
                    'hidden-size': 300,
                    'dropout': 0.1,
                    'epochs': 50,
                    'batch-size': 50
                }
            },
            {
                'name': 'wider_model',
                'params': {
                    'depth': 3,
                    'hidden-size': 500,
                    'dropout': 0.1,
                    'epochs': 50,
                    'batch-size': 50
                }
            },
            {
                'name': 'optimized',
                'params': {
                    'depth': 4,
                    'hidden-size': 500,
                    'dropout': 0.15,
                    'epochs': 80,
                    'batch-size': 32,
                    'learning-rate': 0.0001
                }
            },
            {
                'name': 'ensemble_ready',
                'params': {
                    'depth': 4,
                    'hidden-size': 400,
                    'dropout': 0.2,
                    'epochs': 60,
                    'batch-size': 32,
                    'learning-rate': 0.0005,
                    'ensemble-size': 3  # 集成学习
                }
            }
        ]
        
        # 训练所有配置
        for config in configs:
            self.train_model(config)
        
        # 分析结果
        self.analyze_results()
    
    def analyze_results(self):
        """分析训练结果"""
        print("\n📊 分析训练结果...")
        
        # 过滤成功的结果
        successful_results = [r for r in self.results if r['status'] == 'success']
        
        if not successful_results:
            print("❌ 没有成功的训练结果")
            return
        
        # 创建结果 DataFrame
        results_df = pd.DataFrame(successful_results)
        
        # 保存结果
        results_df.to_csv(os.path.join(self.output_dir, 'training_results.csv'), index=False)
        
        # 打印结果摘要
        print("\n🏆 训练结果摘要:")
        print(results_df[['name', 'mae', 'rmse', 'training_time']].to_string(index=False))
        
        # 找到最佳模型
        best_model = results_df.loc[results_df['mae'].idxmin()]
        print(f"\n🥇 最佳模型: {best_model['name']}")
        print(f"   MAE: {best_model['mae']:.3f}")
        print(f"   RMSE: {best_model['rmse']:.3f}")
        print(f"   参数: {best_model['params']}")
        
        # 可视化结果
        self.visualize_results(results_df)
    
    def visualize_results(self, results_df):
        """可视化训练结果"""
        print("\n📈 生成结果可视化...")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('模型训练结果分析', fontsize=16)
        
        # 1. MAE 对比
        axes[0,0].bar(results_df['name'], results_df['mae'], color='skyblue')
        axes[0,0].set_title('MAE 对比')
        axes[0,0].set_ylabel('MAE')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # 2. RMSE 对比
        axes[0,1].bar(results_df['name'], results_df['rmse'], color='lightgreen')
        axes[0,1].set_title('RMSE 对比')
        axes[0,1].set_ylabel('RMSE')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # 3. 训练时间对比
        axes[1,0].bar(results_df['name'], results_df['training_time'], color='orange')
        axes[1,0].set_title('训练时间对比')
        axes[1,0].set_ylabel('时间 (秒)')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # 4. MAE vs 训练时间
        axes[1,1].scatter(results_df['training_time'], results_df['mae'], s=100, alpha=0.7)
        axes[1,1].set_xlabel('训练时间 (秒)')
        axes[1,1].set_ylabel('MAE')
        axes[1,1].set_title('性能 vs 训练时间')
        
        # 添加模型名称标签
        for i, row in results_df.iterrows():
            axes[1,1].annotate(row['name'], 
                             (row['training_time'], row['mae']),
                             xytext=(5, 5), textcoords='offset points')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'training_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def predict_with_best_model(self, test_smiles, output_file="lipo_predictions.csv"):
        """使用最佳模型进行 lipophilicity 预测"""
        print("\n🔮 使用最佳模型进行 lipophilicity 预测...")
        
        # 找到最佳模型
        successful_results = [r for r in self.results if r['status'] == 'success']
        if not successful_results:
            print("❌ 没有可用的训练模型")
            return
        
        best_result = min(successful_results, key=lambda x: x['mae'])
        best_model_path = os.path.join(self.output_dir, best_result['name'], "model_0", "best.pt")
        
        print(f"使用模型: {best_result['name']}")
        print(f"模型性能: MAE = {best_result['mae']:.3f}, RMSE = {best_result['rmse']:.3f}")
        
        # 准备测试数据
        if isinstance(test_smiles, list):
            test_df = pd.DataFrame({'smiles': test_smiles})
        else:
            test_df = pd.read_csv(test_smiles)
        
        test_file = "temp_test.csv"
        test_df.to_csv(test_file, index=False)
        
        # 进行预测
        predict_cmd = [
            "chemprop", "predict",
            "-i", test_file,
            "--model-paths", best_model_path,
            "-o", output_file
        ]
        
        try:
            subprocess.run(predict_cmd, check=True)
            
            # 读取预测结果
            predictions = pd.read_csv(output_file)
            print(f"✅ 预测完成，结果保存到: {output_file}")
            
            # 显示预测结果
            print("\n🎯 Lipophilicity 预测结果:")
            for i, row in predictions.iterrows():
                smiles = row['smiles']
                lipo_pred = row.iloc[1]  # 第二列是预测值
                print(f"  {smiles}: logP = {lipo_pred:.2f}")
            
            # 清理临时文件
            os.remove(test_file)
            
            return predictions
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 预测失败: {e}")
            return None

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, predictions_file, true_values_file=None):
        """初始化"""
        self.predictions_file = predictions_file
        self.true_values_file = true_values_file
    
    def evaluate_predictions(self):
        """评估预测结果"""
        print("📊 评估预测结果...")
        
        # 读取预测结果
        pred_df = pd.read_csv(self.predictions_file)
        
        if self.true_values_file:
            true_df = pd.read_csv(self.true_values_file)
            merged_df = pred_df.merge(true_df, on='smiles', suffixes=('_pred', '_true'))
        else:
            # 假设预测文件包含真实值
            merged_df = pred_df
        
        # 提取真实值和预测值
        y_true_col = [col for col in merged_df.columns if 'true' in col.lower()]
        y_pred_col = [col for col in merged_df.columns if 'pred' in col.lower() or col == 'logP']
        
        if not y_true_col or not y_pred_col:
            print("❌ 无法找到真实值或预测值列")
            return
        
        y_true = merged_df[y_true_col[0]]
        y_pred = merged_df[y_pred_col[0]]
        
        # 计算评估指标
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        print(f"\n📈 评估指标:")
        print(f"  MAE: {mae:.3f}")
        print(f"  RMSE: {rmse:.3f}")
        print(f"  R²: {r2:.3f}")
        
        # 可视化评估结果
        self.visualize_evaluation(y_true, y_pred, mae, rmse, r2)
        
        return {'mae': mae, 'rmse': rmse, 'r2': r2}
    
    def visualize_evaluation(self, y_true, y_pred, mae, rmse, r2):
        """可视化评估结果"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('模型评估结果', fontsize=16)
        
        # 1. 预测 vs 真实值
        axes[0,0].scatter(y_true, y_pred, alpha=0.7)
        axes[0,0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[0,0].set_xlabel('真实值')
        axes[0,0].set_ylabel('预测值')
        axes[0,0].set_title(f'预测 vs 真实值 (R² = {r2:.3f})')
        
        # 2. 残差图
        residuals = y_pred - y_true
        axes[0,1].scatter(y_pred, residuals, alpha=0.7)
        axes[0,1].axhline(y=0, color='r', linestyle='--')
        axes[0,1].set_xlabel('预测值')
        axes[0,1].set_ylabel('残差')
        axes[0,1].set_title('残差分析')
        
        # 3. 误差分布
        axes[1,0].hist(residuals, bins=15, alpha=0.7, color='skyblue')
        axes[1,0].set_xlabel('残差')
        axes[1,0].set_ylabel('频次')
        axes[1,0].set_title(f'误差分布 (MAE = {mae:.3f})')
        
        # 4. 绝对误差分布
        abs_errors = np.abs(residuals)
        axes[1,1].hist(abs_errors, bins=15, alpha=0.7, color='orange')
        axes[1,1].set_xlabel('绝对误差')
        axes[1,1].set_ylabel('频次')
        axes[1,1].set_title(f'绝对误差分布 (RMSE = {rmse:.3f})')
        
        plt.tight_layout()
        plt.savefig('model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

def validate_data(data_path):
    """验证训练数据格式"""
    print(f"🔍 验证数据文件: {data_path}")
    
    try:
        df = pd.read_csv(data_path)
        print(f"   数据集大小: {len(df)} 个分子")
        print(f"   数据列: {list(df.columns)}")
        
        # 检查必需的列
        required_cols = ['smiles', 'lipo']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺少必需的列: {missing_cols}")
            return False
        
        # 检查数据范围
        lipo_min, lipo_max = df['lipo'].min(), df['lipo'].max()
        lipo_mean, lipo_std = df['lipo'].mean(), df['lipo'].std()
        
        print(f"   Lipophilicity 范围: {lipo_min:.2f} ~ {lipo_max:.2f}")
        print(f"   Lipophilicity 均值±标准差: {lipo_mean:.2f} ± {lipo_std:.2f}")
        
        # 检查是否有缺失值
        missing_smiles = df['smiles'].isna().sum()
        missing_lipo = df['lipo'].isna().sum()
        
        if missing_smiles > 0 or missing_lipo > 0:
            print(f"⚠️ 发现缺失值: SMILES({missing_smiles}), lipo({missing_lipo})")
        else:
            print("✅ 数据格式验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 ChemProp Lipophilicity 预测模型训练")
    print("=" * 60)
    
    # 检查和验证数据文件
    data_file = "official_lipo_data.csv"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        print("请确保 official_lipo_data.csv 文件在当前目录下")
        return
    
    # 验证数据格式
    if not validate_data(data_file):
        print("❌ 数据验证失败，请检查数据格式")
        return
    
    # 创建训练器
    trainer = ChemPropTrainer(data_file, output_dir="lipo_models")
    
    # 进行超参数搜索
    trainer.hyperparameter_search()
    
    # 测试预测 - 使用一些有代表性的分子
    test_molecules = [
        'CCO',  # 乙醇 (亲水性)
        'c1ccccc1',  # 苯 (疏水性)
        'CC(C)O',  # 异丙醇 (中等)
        'CCCCCCCC',  # 辛烷 (高疏水性)
        'COc1ccccc1',  # 苯甲醚 (芳香族醚)
        'CC(=O)O',  # 乙酸 (极性)
        'c1ccc(cc1)O'  # 苯酚 (含羟基芳香族)
    ]
    
    print(f"\n🧪 使用 {len(test_molecules)} 个测试分子进行预测...")
    predictions = trainer.predict_with_best_model(test_molecules)
    
    print("\n🎉 Lipophilicity 预测模型训练和评估完成!")
    print("📊 查看 lipo_models/ 目录获取详细结果")
    print("📈 查看 training_analysis.png 获取性能分析图表")

if __name__ == "__main__":
    main()



