{"version": 3, "file": "5828.66806b64a5e5ffda935f.js?v=66806b64a5e5ffda935f", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAA8H;AAC3E;AACK;AACa;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,wDAAwD;AACxD,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kEAAiB;AAChC;AACA,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,8BAA8B,yDAAQ;AACtC;AACA,yFAAyF,IAAI,UAAU,sJAAsJ,IAAI,0DAA0D,wDAAwD,UAAU;AAC7X;AACA;AACA,yEAAyE,MAAM;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,gBAAgB;AAC7B;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gEAAU;AACxB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,0BAA0B,YAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,MAAM;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,QAAQ,kFAAkF;AACzJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,wCAAwC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,+BAA+B;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,2EAAO,mFAAmF,oFAAgB;AACrH;;AAEA;AACA;AACA,qBAAqB,gEAAc;AACnC,oCAAoC,qEAAe;AACnD,SAAS;AACT,qBAAqB,8DAAY;AACjC,qCAAqC,SAAS,iFAAiF;AAC/H,iCAAiC,SAAS;AAC1C,SAAS;AACT,qBAAqB,2DAAS;AAC9B,qBAAqB,kDAAI;AACzB,kBAAkB,kDAAI;AACtB,kCAAkC,kDAAI,UAAU,kDAAI;AACpD,kBAAkB,kDAAI;AACtB,mBAAmB,kDAAI;AACvB,kBAAkB,kDAAI;AACtB,kBAAkB,kDAAI;AACtB,oBAAoB,kDAAI;AACxB,oBAAoB,kDAAI;AACxB,wBAAwB,kDAAI;AAC5B,2CAA2C,kDAAI,SAAS,kDAAI;AAC5D,qCAAqC,kDAAI,SAAS,kDAAI;AACtD,yBAAyB,kDAAI;AAC7B,0BAA0B,kDAAI;AAC9B,sBAAsB,kDAAI;AAC1B,gCAAgC,kDAAI;AACpC,mBAAmB,kDAAI;AACvB,gBAAgB,GAAG,kDAAI;AACvB,mBAAmB,kDAAI;AACvB,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,uBAAuB,4DAAU;AACjC;AACA;AACA,+BAA+B,gCAAgC;AAC/D,aAAa;AACb;AACA,iCAAiC,qBAAqB,2BAA2B;AACjF,iCAAiC,uBAAuB;AACxD;AACA,SAAS;AACT;AACA;AACA;AACA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,eAAe,iEAAe;AAC9B;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAEsJ", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/lang-sql/dist/index.js"], "sourcesContent": ["import { syntaxTree, indentNodeProp, continuedIndent, foldNodeProp, LRLanguage, LanguageSupport } from '@codemirror/language';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { ifNotIn, completeFromList } from '@codemirror/autocomplete';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst whitespace = 36,\n  LineComment = 1,\n  BlockComment = 2,\n  String$1 = 3,\n  Number = 4,\n  Bool = 5,\n  Null = 6,\n  ParenL = 7,\n  ParenR = 8,\n  BraceL = 9,\n  BraceR = 10,\n  BracketL = 11,\n  BracketR = 12,\n  Semi = 13,\n  Dot = 14,\n  Operator = 15,\n  Punctuation = 16,\n  SpecialVar = 17,\n  Identifier = 18,\n  QuotedIdentifier = 19,\n  Keyword = 20,\n  Type = 21,\n  Bits = 22,\n  Bytes = 23,\n  Builtin = 24;\n\nfunction isAlpha(ch) {\n    return ch >= 65 /* Ch.A */ && ch <= 90 /* Ch.Z */ || ch >= 97 /* Ch.a */ && ch <= 122 /* Ch.z */ || ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */;\n}\nfunction isHexDigit(ch) {\n    return ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */ || ch >= 97 /* Ch.a */ && ch <= 102 /* Ch.f */ || ch >= 65 /* Ch.A */ && ch <= 70 /* Ch.F */;\n}\nfunction readLiteral(input, endQuote, backslashEscapes) {\n    for (let escaped = false;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == endQuote && !escaped) {\n            input.advance();\n            return;\n        }\n        escaped = backslashEscapes && !escaped && input.next == 92 /* Ch.Backslash */;\n        input.advance();\n    }\n}\nfunction readDoubleDollarLiteral(input, tag) {\n    scan: for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == 36 /* Ch.Dollar */) {\n            input.advance();\n            for (let i = 0; i < tag.length; i++) {\n                if (input.next != tag.charCodeAt(i))\n                    continue scan;\n                input.advance();\n            }\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                return;\n            }\n        }\n        else {\n            input.advance();\n        }\n    }\n}\nfunction readPLSQLQuotedLiteral(input, openDelim) {\n    let matchingDelim = \"[{<(\".indexOf(String.fromCharCode(openDelim));\n    let closeDelim = matchingDelim < 0 ? openDelim : \"]}>)\".charCodeAt(matchingDelim);\n    for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == closeDelim && input.peek(1) == 39 /* Ch.SingleQuote */) {\n            input.advance(2);\n            return;\n        }\n        input.advance();\n    }\n}\nfunction readWord(input, result) {\n    for (;;) {\n        if (input.next != 95 /* Ch.Underscore */ && !isAlpha(input.next))\n            break;\n        if (result != null)\n            result += String.fromCharCode(input.next);\n        input.advance();\n    }\n    return result;\n}\nfunction readWordOrQuoted(input) {\n    if (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */ || input.next == 96 /* Ch.Backtick */) {\n        let quote = input.next;\n        input.advance();\n        readLiteral(input, quote, false);\n    }\n    else {\n        readWord(input);\n    }\n}\nfunction readBits(input, endQuote) {\n    while (input.next == 48 /* Ch._0 */ || input.next == 49 /* Ch._1 */)\n        input.advance();\n    if (endQuote && input.next == endQuote)\n        input.advance();\n}\nfunction readNumber(input, sawDot) {\n    for (;;) {\n        if (input.next == 46 /* Ch.Dot */) {\n            if (sawDot)\n                break;\n            sawDot = true;\n        }\n        else if (input.next < 48 /* Ch._0 */ || input.next > 57 /* Ch._9 */) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.next == 69 /* Ch.E */ || input.next == 101 /* Ch.e */) {\n        input.advance();\n        if (input.next == 43 /* Ch.Plus */ || input.next == 45 /* Ch.Dash */)\n            input.advance();\n        while (input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */)\n            input.advance();\n    }\n}\nfunction eol(input) {\n    while (!(input.next < 0 || input.next == 10 /* Ch.Newline */))\n        input.advance();\n}\nfunction inString(ch, str) {\n    for (let i = 0; i < str.length; i++)\n        if (str.charCodeAt(i) == ch)\n            return true;\n    return false;\n}\nconst Space = \" \\t\\r\\n\";\nfunction keywords(keywords, types, builtin) {\n    let result = Object.create(null);\n    result[\"true\"] = result[\"false\"] = Bool;\n    result[\"null\"] = result[\"unknown\"] = Null;\n    for (let kw of keywords.split(\" \"))\n        if (kw)\n            result[kw] = Keyword;\n    for (let tp of types.split(\" \"))\n        if (tp)\n            result[tp] = Type;\n    for (let kw of (builtin || \"\").split(\" \"))\n        if (kw)\n            result[kw] = Builtin;\n    return result;\n}\nconst SQLTypes = \"array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying \";\nconst SQLKeywords = \"absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone \";\nconst defaults = {\n    backslashEscapes: false,\n    hashComments: false,\n    spaceAfterDashes: false,\n    slashComments: false,\n    doubleQuotedStrings: false,\n    doubleDollarQuotedStrings: false,\n    unquotedBitLiterals: false,\n    treatBitsAsBytes: false,\n    charSetCasts: false,\n    plsqlQuotingMechanism: false,\n    operatorChars: \"*+\\-%<>!=&|~^/\",\n    specialVar: \"?\",\n    identifierQuotes: '\"',\n    caseInsensitiveIdentifiers: false,\n    words: /*@__PURE__*/keywords(SQLKeywords, SQLTypes)\n};\nfunction dialect(spec, kws, types, builtin) {\n    let dialect = {};\n    for (let prop in defaults)\n        dialect[prop] = (spec.hasOwnProperty(prop) ? spec : defaults)[prop];\n    if (kws)\n        dialect.words = keywords(kws, types || \"\", builtin);\n    return dialect;\n}\nfunction tokensFor(d) {\n    return new ExternalTokenizer(input => {\n        var _a;\n        let { next } = input;\n        input.advance();\n        if (inString(next, Space)) {\n            while (inString(input.next, Space))\n                input.advance();\n            input.acceptToken(whitespace);\n        }\n        else if (next == 36 /* Ch.Dollar */ && d.doubleDollarQuotedStrings) {\n            let tag = readWord(input, \"\");\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                readDoubleDollarLiteral(input, tag);\n                input.acceptToken(String$1);\n            }\n        }\n        else if (next == 39 /* Ch.SingleQuote */ || next == 34 /* Ch.DoubleQuote */ && d.doubleQuotedStrings) {\n            readLiteral(input, next, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 35 /* Ch.Hash */ && d.hashComments ||\n            next == 47 /* Ch.Slash */ && input.next == 47 /* Ch.Slash */ && d.slashComments) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 45 /* Ch.Dash */ && input.next == 45 /* Ch.Dash */ &&\n            (!d.spaceAfterDashes || input.peek(1) == 32 /* Ch.Space */)) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n            input.advance();\n            for (let depth = 1;;) {\n                let cur = input.next;\n                if (input.next < 0)\n                    break;\n                input.advance();\n                if (cur == 42 /* Ch.Star */ && input.next == 47 /* Ch.Slash */) {\n                    depth--;\n                    input.advance();\n                    if (!depth)\n                        break;\n                }\n                else if (cur == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n                    depth++;\n                    input.advance();\n                }\n            }\n            input.acceptToken(BlockComment);\n        }\n        else if ((next == 101 /* Ch.e */ || next == 69 /* Ch.E */) && input.next == 39 /* Ch.SingleQuote */) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, true);\n            input.acceptToken(String$1);\n        }\n        else if ((next == 110 /* Ch.n */ || next == 78 /* Ch.N */) && input.next == 39 /* Ch.SingleQuote */ &&\n            d.charSetCasts) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 95 /* Ch.Underscore */ && d.charSetCasts) {\n            for (let i = 0;; i++) {\n                if (input.next == 39 /* Ch.SingleQuote */ && i > 1) {\n                    input.advance();\n                    readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n                    input.acceptToken(String$1);\n                    break;\n                }\n                if (!isAlpha(input.next))\n                    break;\n                input.advance();\n            }\n        }\n        else if (d.plsqlQuotingMechanism &&\n            (next == 113 /* Ch.q */ || next == 81 /* Ch.Q */) && input.next == 39 /* Ch.SingleQuote */ &&\n            input.peek(1) > 0 && !inString(input.peek(1), Space)) {\n            let openDelim = input.peek(1);\n            input.advance(2);\n            readPLSQLQuotedLiteral(input, openDelim);\n            input.acceptToken(String$1);\n        }\n        else if (next == 40 /* Ch.ParenL */) {\n            input.acceptToken(ParenL);\n        }\n        else if (next == 41 /* Ch.ParenR */) {\n            input.acceptToken(ParenR);\n        }\n        else if (next == 123 /* Ch.BraceL */) {\n            input.acceptToken(BraceL);\n        }\n        else if (next == 125 /* Ch.BraceR */) {\n            input.acceptToken(BraceR);\n        }\n        else if (next == 91 /* Ch.BracketL */) {\n            input.acceptToken(BracketL);\n        }\n        else if (next == 93 /* Ch.BracketR */) {\n            input.acceptToken(BracketR);\n        }\n        else if (next == 59 /* Ch.Semi */) {\n            input.acceptToken(Semi);\n        }\n        else if (d.unquotedBitLiterals && next == 48 /* Ch._0 */ && input.next == 98 /* Ch.b */) {\n            input.advance();\n            readBits(input);\n            input.acceptToken(Bits);\n        }\n        else if ((next == 98 /* Ch.b */ || next == 66 /* Ch.B */) && (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */)) {\n            const quoteStyle = input.next;\n            input.advance();\n            if (d.treatBitsAsBytes) {\n                readLiteral(input, quoteStyle, d.backslashEscapes);\n                input.acceptToken(Bytes);\n            }\n            else {\n                readBits(input, quoteStyle);\n                input.acceptToken(Bits);\n            }\n        }\n        else if (next == 48 /* Ch._0 */ && (input.next == 120 /* Ch.x */ || input.next == 88 /* Ch.X */) ||\n            (next == 120 /* Ch.x */ || next == 88 /* Ch.X */) && input.next == 39 /* Ch.SingleQuote */) {\n            let quoted = input.next == 39 /* Ch.SingleQuote */;\n            input.advance();\n            while (isHexDigit(input.next))\n                input.advance();\n            if (quoted && input.next == 39 /* Ch.SingleQuote */)\n                input.advance();\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */ && input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */) {\n            readNumber(input, true);\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */) {\n            input.acceptToken(Dot);\n        }\n        else if (next >= 48 /* Ch._0 */ && next <= 57 /* Ch._9 */) {\n            readNumber(input, false);\n            input.acceptToken(Number);\n        }\n        else if (inString(next, d.operatorChars)) {\n            while (inString(input.next, d.operatorChars))\n                input.advance();\n            input.acceptToken(Operator);\n        }\n        else if (inString(next, d.specialVar)) {\n            if (input.next == next)\n                input.advance();\n            readWordOrQuoted(input);\n            input.acceptToken(SpecialVar);\n        }\n        else if (inString(next, d.identifierQuotes)) {\n            readLiteral(input, next, false);\n            input.acceptToken(QuotedIdentifier);\n        }\n        else if (next == 58 /* Ch.Colon */ || next == 44 /* Ch.Comma */) {\n            input.acceptToken(Punctuation);\n        }\n        else if (isAlpha(next)) {\n            let word = readWord(input, String.fromCharCode(next));\n            input.acceptToken(input.next == 46 /* Ch.Dot */ || input.peek(-word.length - 1) == 46 /* Ch.Dot */\n                ? Identifier : (_a = d.words[word.toLowerCase()]) !== null && _a !== void 0 ? _a : Identifier);\n        }\n    });\n}\nconst tokens = /*@__PURE__*/tokensFor(defaults);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser$1 = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw\",\n  stateData: \",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O\",\n  goto: \"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq\",\n  nodeNames: \"⚠ LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement\",\n  maxTerm: 38,\n  nodeProps: [\n    [\"isolate\", -4,1,2,3,19,\"\"]\n  ],\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 3,\n  tokenData: \"RORO\",\n  tokenizers: [0, tokens],\n  topRules: {\"Script\":[0,25]},\n  tokenPrec: 0\n});\n\nfunction tokenBefore(tree) {\n    let cursor = tree.cursor().moveTo(tree.from, -1);\n    while (/Comment/.test(cursor.name))\n        cursor.moveTo(cursor.from, -1);\n    return cursor.node;\n}\nfunction idName(doc, node) {\n    let text = doc.sliceString(node.from, node.to);\n    let quoted = /^([`'\"])(.*)\\1$/.exec(text);\n    return quoted ? quoted[2] : text;\n}\nfunction plainID(node) {\n    return node && (node.name == \"Identifier\" || node.name == \"QuotedIdentifier\");\n}\nfunction pathFor(doc, id) {\n    if (id.name == \"CompositeIdentifier\") {\n        let path = [];\n        for (let ch = id.firstChild; ch; ch = ch.nextSibling)\n            if (plainID(ch))\n                path.push(idName(doc, ch));\n        return path;\n    }\n    return [idName(doc, id)];\n}\nfunction parentsFor(doc, node) {\n    for (let path = [];;) {\n        if (!node || node.name != \".\")\n            return path;\n        let name = tokenBefore(node);\n        if (!plainID(name))\n            return path;\n        path.unshift(idName(doc, name));\n        node = tokenBefore(name);\n    }\n}\nfunction sourceContext(state, startPos) {\n    let pos = syntaxTree(state).resolveInner(startPos, -1);\n    let aliases = getAliases(state.doc, pos);\n    if (pos.name == \"Identifier\" || pos.name == \"QuotedIdentifier\" || pos.name == \"Keyword\") {\n        return { from: pos.from,\n            quoted: pos.name == \"QuotedIdentifier\" ? state.doc.sliceString(pos.from, pos.from + 1) : null,\n            parents: parentsFor(state.doc, tokenBefore(pos)),\n            aliases };\n    }\n    if (pos.name == \".\") {\n        return { from: startPos, quoted: null, parents: parentsFor(state.doc, pos), aliases };\n    }\n    else {\n        return { from: startPos, quoted: null, parents: [], empty: true, aliases };\n    }\n}\nconst EndFrom = /*@__PURE__*/new Set(/*@__PURE__*/\"where group having order union intersect except all distinct limit offset fetch for\".split(\" \"));\nfunction getAliases(doc, at) {\n    let statement;\n    for (let parent = at; !statement; parent = parent.parent) {\n        if (!parent)\n            return null;\n        if (parent.name == \"Statement\")\n            statement = parent;\n    }\n    let aliases = null;\n    for (let scan = statement.firstChild, sawFrom = false, prevID = null; scan; scan = scan.nextSibling) {\n        let kw = scan.name == \"Keyword\" ? doc.sliceString(scan.from, scan.to).toLowerCase() : null;\n        let alias = null;\n        if (!sawFrom) {\n            sawFrom = kw == \"from\";\n        }\n        else if (kw == \"as\" && prevID && plainID(scan.nextSibling)) {\n            alias = idName(doc, scan.nextSibling);\n        }\n        else if (kw && EndFrom.has(kw)) {\n            break;\n        }\n        else if (prevID && plainID(scan)) {\n            alias = idName(doc, scan);\n        }\n        if (alias) {\n            if (!aliases)\n                aliases = Object.create(null);\n            aliases[alias] = pathFor(doc, prevID);\n        }\n        prevID = /Identifier$/.test(scan.name) ? scan : null;\n    }\n    return aliases;\n}\nfunction maybeQuoteCompletions(quote, completions) {\n    if (!quote)\n        return completions;\n    return completions.map(c => (Object.assign(Object.assign({}, c), { label: c.label[0] == quote ? c.label : quote + c.label + quote, apply: undefined })));\n}\nconst Span = /^\\w*$/, QuotedSpan = /^[`'\"]?\\w*[`'\"]?$/;\nfunction isSelfTag(namespace) {\n    return namespace.self && typeof namespace.self.label == \"string\";\n}\nclass CompletionLevel {\n    constructor(idQuote, idCaseInsensitive) {\n        this.idQuote = idQuote;\n        this.idCaseInsensitive = idCaseInsensitive;\n        this.list = [];\n        this.children = undefined;\n    }\n    child(name) {\n        let children = this.children || (this.children = Object.create(null));\n        let found = children[name];\n        if (found)\n            return found;\n        if (name && !this.list.some(c => c.label == name))\n            this.list.push(nameCompletion(name, \"type\", this.idQuote, this.idCaseInsensitive));\n        return (children[name] = new CompletionLevel(this.idQuote, this.idCaseInsensitive));\n    }\n    maybeChild(name) {\n        return this.children ? this.children[name] : null;\n    }\n    addCompletion(option) {\n        let found = this.list.findIndex(o => o.label == option.label);\n        if (found > -1)\n            this.list[found] = option;\n        else\n            this.list.push(option);\n    }\n    addCompletions(completions) {\n        for (let option of completions)\n            this.addCompletion(typeof option == \"string\" ? nameCompletion(option, \"property\", this.idQuote, this.idCaseInsensitive) : option);\n    }\n    addNamespace(namespace) {\n        if (Array.isArray(namespace)) {\n            this.addCompletions(namespace);\n        }\n        else if (isSelfTag(namespace)) {\n            this.addNamespace(namespace.children);\n        }\n        else {\n            this.addNamespaceObject(namespace);\n        }\n    }\n    addNamespaceObject(namespace) {\n        for (let name of Object.keys(namespace)) {\n            let children = namespace[name], self = null;\n            let parts = name.replace(/\\\\?\\./g, p => p == \".\" ? \"\\0\" : p).split(\"\\0\");\n            let scope = this;\n            if (isSelfTag(children)) {\n                self = children.self;\n                children = children.children;\n            }\n            for (let i = 0; i < parts.length; i++) {\n                if (self && i == parts.length - 1)\n                    scope.addCompletion(self);\n                scope = scope.child(parts[i].replace(/\\\\\\./g, \".\"));\n            }\n            scope.addNamespace(children);\n        }\n    }\n}\nfunction nameCompletion(label, type, idQuote, idCaseInsensitive) {\n    if ((new RegExp(\"^[a-z_][a-z_\\\\d]*$\", idCaseInsensitive ? \"i\" : \"\")).test(label))\n        return { label, type };\n    return { label, type, apply: idQuote + label + idQuote };\n}\n// Some of this is more gnarly than it has to be because we're also\n// supporting the deprecated, not-so-well-considered style of\n// supplying the schema (dotted property names for schemas, separate\n// `tables` and `schemas` completions).\nfunction completeFromSchema(schema, tables, schemas, defaultTableName, defaultSchemaName, dialect) {\n    var _a;\n    let idQuote = ((_a = dialect === null || dialect === void 0 ? void 0 : dialect.spec.identifierQuotes) === null || _a === void 0 ? void 0 : _a[0]) || '\"';\n    let top = new CompletionLevel(idQuote, !!(dialect === null || dialect === void 0 ? void 0 : dialect.spec.caseInsensitiveIdentifiers));\n    let defaultSchema = defaultSchemaName ? top.child(defaultSchemaName) : null;\n    top.addNamespace(schema);\n    if (tables)\n        (defaultSchema || top).addCompletions(tables);\n    if (schemas)\n        top.addCompletions(schemas);\n    if (defaultSchema)\n        top.addCompletions(defaultSchema.list);\n    if (defaultTableName)\n        top.addCompletions((defaultSchema || top).child(defaultTableName).list);\n    return (context) => {\n        let { parents, from, quoted, empty, aliases } = sourceContext(context.state, context.pos);\n        if (empty && !context.explicit)\n            return null;\n        if (aliases && parents.length == 1)\n            parents = aliases[parents[0]] || parents;\n        let level = top;\n        for (let name of parents) {\n            while (!level.children || !level.children[name]) {\n                if (level == top && defaultSchema)\n                    level = defaultSchema;\n                else if (level == defaultSchema && defaultTableName)\n                    level = level.child(defaultTableName);\n                else\n                    return null;\n            }\n            let next = level.maybeChild(name);\n            if (!next)\n                return null;\n            level = next;\n        }\n        let quoteAfter = quoted && context.state.sliceDoc(context.pos, context.pos + 1) == quoted;\n        let options = level.list;\n        if (level == top && aliases)\n            options = options.concat(Object.keys(aliases).map(name => ({ label: name, type: \"constant\" })));\n        return {\n            from,\n            to: quoteAfter ? context.pos + 1 : undefined,\n            options: maybeQuoteCompletions(quoted, options),\n            validFor: quoted ? QuotedSpan : Span\n        };\n    };\n}\nfunction completionType(tokenType) {\n    return tokenType == Type ? \"type\" : tokenType == Keyword ? \"keyword\" : \"variable\";\n}\nfunction completeKeywords(keywords, upperCase, build) {\n    let completions = Object.keys(keywords)\n        .map(keyword => build(upperCase ? keyword.toUpperCase() : keyword, completionType(keywords[keyword])));\n    return ifNotIn([\"QuotedIdentifier\", \"SpecialVar\", \"String\", \"LineComment\", \"BlockComment\", \".\"], completeFromList(completions));\n}\n\nlet parser = /*@__PURE__*/parser$1.configure({\n    props: [\n        /*@__PURE__*/indentNodeProp.add({\n            Statement: /*@__PURE__*/continuedIndent()\n        }),\n        /*@__PURE__*/foldNodeProp.add({\n            Statement(tree, state) { return { from: Math.min(tree.from + 100, state.doc.lineAt(tree.from).to), to: tree.to }; },\n            BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n        }),\n        /*@__PURE__*/styleTags({\n            Keyword: tags.keyword,\n            Type: tags.typeName,\n            Builtin: /*@__PURE__*/tags.standard(tags.name),\n            Bits: tags.number,\n            Bytes: tags.string,\n            Bool: tags.bool,\n            Null: tags.null,\n            Number: tags.number,\n            String: tags.string,\n            Identifier: tags.name,\n            QuotedIdentifier: /*@__PURE__*/tags.special(tags.string),\n            SpecialVar: /*@__PURE__*/tags.special(tags.name),\n            LineComment: tags.lineComment,\n            BlockComment: tags.blockComment,\n            Operator: tags.operator,\n            \"Semi Punctuation\": tags.punctuation,\n            \"( )\": tags.paren,\n            \"{ }\": tags.brace,\n            \"[ ]\": tags.squareBracket\n        })\n    ]\n});\n/**\nRepresents an SQL dialect.\n*/\nclass SQLDialect {\n    constructor(\n    /**\n    @internal\n    */\n    dialect, \n    /**\n    The language for this dialect.\n    */\n    language, \n    /**\n    The spec used to define this dialect.\n    */\n    spec) {\n        this.dialect = dialect;\n        this.language = language;\n        this.spec = spec;\n    }\n    /**\n    Returns the language for this dialect as an extension.\n    */\n    get extension() { return this.language.extension; }\n    /**\n    Define a new dialect.\n    */\n    static define(spec) {\n        let d = dialect(spec, spec.keywords, spec.types, spec.builtin);\n        let language = LRLanguage.define({\n            name: \"sql\",\n            parser: parser.configure({\n                tokenizers: [{ from: tokens, to: tokensFor(d) }]\n            }),\n            languageData: {\n                commentTokens: { line: \"--\", block: { open: \"/*\", close: \"*/\" } },\n                closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] }\n            }\n        });\n        return new SQLDialect(d, language, spec);\n    }\n}\nfunction defaultKeyword(label, type) { return { label, type, boost: -1 }; }\n/**\nReturns a completion source that provides keyword completion for\nthe given SQL dialect.\n*/\nfunction keywordCompletionSource(dialect, upperCase = false, build) {\n    return completeKeywords(dialect.dialect.words, upperCase, build || defaultKeyword);\n}\n/**\nReturns a completion sources that provides schema-based completion\nfor the given configuration.\n*/\nfunction schemaCompletionSource(config) {\n    return config.schema ? completeFromSchema(config.schema, config.tables, config.schemas, config.defaultTable, config.defaultSchema, config.dialect || StandardSQL)\n        : () => null;\n}\nfunction schemaCompletion(config) {\n    return config.schema ? (config.dialect || StandardSQL).language.data.of({\n        autocomplete: schemaCompletionSource(config)\n    }) : [];\n}\n/**\nSQL language support for the given SQL dialect, with keyword\ncompletion, and, if provided, schema-based completion as extra\nextensions.\n*/\nfunction sql(config = {}) {\n    let lang = config.dialect || StandardSQL;\n    return new LanguageSupport(lang.language, [\n        schemaCompletion(config),\n        lang.language.data.of({\n            autocomplete: keywordCompletionSource(lang, config.upperCaseKeywords, config.keywordCompletion)\n        })\n    ]);\n}\n/**\nThe standard SQL dialect.\n*/\nconst StandardSQL = /*@__PURE__*/SQLDialect.define({});\n/**\nDialect for [PostgreSQL](https://www.postgresql.org).\n*/\nconst PostgreSQL = /*@__PURE__*/SQLDialect.define({\n    charSetCasts: true,\n    doubleDollarQuotedStrings: true,\n    operatorChars: \"+-*/<>=~!@#%^&|`?\",\n    specialVar: \"\",\n    keywords: SQLKeywords + \"abort abs absent access according ada admin aggregate alias also always analyse analyze array_agg array_max_cardinality asensitive assert assignment asymmetric atomic attach attribute attributes avg backward base64 begin_frame begin_partition bernoulli bit_length blocked bom cache called cardinality catalog_name ceil ceiling chain char_length character_length character_set_catalog character_set_name character_set_schema characteristics characters checkpoint class class_origin cluster coalesce cobol collation_catalog collation_name collation_schema collect column_name columns command_function command_function_code comment comments committed concurrently condition_number configuration conflict connection_name constant constraint_catalog constraint_name constraint_schema contains content control conversion convert copy corr cost covar_pop covar_samp csv cume_dist current_catalog current_row current_schema cursor_name database datalink datatype datetime_interval_code datetime_interval_precision db debug defaults defined definer degree delimiter delimiters dense_rank depends derived detach detail dictionary disable discard dispatch dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue document dump dynamic_function dynamic_function_code element elsif empty enable encoding encrypted end_frame end_partition endexec enforced enum errcode error event every exclude excluding exclusive exp explain expression extension extract family file filter final first_value flag floor following force foreach fortran forward frame_row freeze fs functions fusion generated granted greatest groups handler header hex hierarchy hint id ignore ilike immediately immutable implementation implicit import include including increment indent index indexes info inherit inherits inline insensitive instance instantiable instead integrity intersection invoker isnull key_member key_type label lag last_value lead leakproof least length library like_regex link listen ln load location lock locked log logged lower mapping matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text min minvalue mod mode more move multiset mumps name namespace nfc nfd nfkc nfkd nil normalize normalized nothing notice notify notnull nowait nth_value ntile nullable nullif nulls number occurrences_regex octet_length octets off offset oids operator options ordering others over overlay overriding owned owner parallel parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partition pascal passing passthrough password percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding prepared print_strict_params procedural procedures program publication query quote raise range rank reassign recheck recovery refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex rename repeatable replace replica requiring reset respect restart restore result_oid returned_cardinality returned_length returned_octet_length returned_sqlstate returning reverse routine_catalog routine_name routine_schema routines row_count row_number rowtype rule scale schema_name schemas scope scope_catalog scope_name scope_schema security selective self sensitive sequence sequences serializable server server_name setof share show simple skip slice snapshot source specific_name sqlcode sqlerror sqrt stable stacked standalone statement statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time table_name tables tablesample tablespace temp template ties token top_level_count transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex trigger_catalog trigger_name trigger_schema trim trim_array truncate trusted type types uescape unbounded uncommitted unencrypted unlink unlisten unlogged unnamed untyped upper uri use_column use_variable user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema vacuum valid validate validator value_of var_pop var_samp varbinary variable_conflict variadic verbose version versioning views volatile warning whitespace width_bucket window within wrapper xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate yes\",\n    types: SQLTypes + \"bigint int8 bigserial serial8 varbit bool box bytea cidr circle precision float8 inet int4 json jsonb line lseg macaddr macaddr8 money numeric pg_lsn point polygon float4 int2 smallserial serial2 serial serial4 text timetz timestamptz tsquery tsvector txid_snapshot uuid xml\"\n});\nconst MySQLKeywords = \"accessible algorithm analyze asensitive authors auto_increment autocommit avg avg_row_length binlog btree cache catalog_name chain change changed checkpoint checksum class_origin client_statistics coalesce code collations columns comment committed completion concurrent consistent contains contributors convert database databases day_hour day_microsecond day_minute day_second delay_key_write delayed delimiter des_key_file dev_pop dev_samp deviance directory disable discard distinctrow div dual dumpfile enable enclosed ends engine engines enum errors escaped even event events every explain extended fast field fields flush force found_rows fulltext grants handler hash high_priority hosts hour_microsecond hour_minute hour_second ignore ignore_server_ids import index index_statistics infile innodb insensitive insert_method install invoker iterate keys kill linear lines list load lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modify mutex mysql_errno no_write_to_binlog offline offset one online optimize optionally outfile pack_keys parser partition partitions password phase plugin plugins prev processlist profile profiles purge query quick range read_write rebuild recover regexp relaylog remove rename reorganize repair repeatable replace require resume rlike row_format rtree schedule schema_name schemas second_microsecond security sensitive separator serializable server share show slave slow snapshot soname spatial sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result ssl starting starts std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace terminated triggers truncate uncommitted uninstall unlock upgrade use use_frm user_resources user_statistics utc_date utc_time utc_timestamp variables views warnings xa xor year_month zerofill\";\nconst MySQLTypes = SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int1 int2 int3 int4 int8 float4 float8 varbinary varcharacter precision datetime unsigned signed\";\nconst MySQLBuiltin = \"charset clear edit ego help nopager notee nowarning pager print prompt quit rehash source status system tee\";\n/**\n[MySQL](https://dev.mysql.com/) dialect.\n*/\nconst MySQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"group_concat \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nVariant of [`MySQL`](https://codemirror.net/6/docs/ref/#lang-sql.MySQL) for\n[MariaDB](https://mariadb.org/).\n*/\nconst MariaSQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"always generated groupby_concat hard persistent shutdown soft virtual \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nSQL dialect for Microsoft [SQL\nServer](https://www.microsoft.com/en-us/sql-server).\n*/\nconst MSSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock pivot readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx unpivot updlock with\",\n    types: SQLTypes + \"bigint smallint smallmoney tinyint money real text nvarchar ntext varbinary image hierarchyid uniqueidentifier sql_variant xml\",\n    builtin: \"binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\",\n    operatorChars: \"*+-%<>!=^&|/\",\n    specialVar: \"@\"\n});\n/**\n[SQLite](https://sqlite.org/) dialect.\n*/\nconst SQLite = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort analyze attach autoincrement conflict database detach exclusive fail glob ignore index indexed instead isnull notnull offset plan pragma query raise regexp reindex rename replace temp vacuum virtual\",\n    types: SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int2 int8 unsigned signed real\",\n    builtin: \"auth backup bail changes clone databases dbinfo dump echo eqp explain fullschema headers help import imposter indexes iotrace lint load log mode nullvalue once print prompt quit restore save scanstats separator shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\",\n    operatorChars: \"*+-%<>!=&|/~\",\n    identifierQuotes: \"`\\\"\",\n    specialVar: \"@:?$\"\n});\n/**\nDialect for [Cassandra](https://cassandra.apache.org/)'s SQL-ish query language.\n*/\nconst Cassandra = /*@__PURE__*/SQLDialect.define({\n    keywords: \"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime infinity NaN\",\n    types: SQLTypes + \"ascii bigint blob counter frozen inet list map static text timeuuid tuple uuid varint\",\n    slashComments: true\n});\n/**\n[PL/SQL](https://en.wikipedia.org/wiki/PL/SQL) dialect.\n*/\nconst PLSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort accept access add all alter and any arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body by case cast char_base check close cluster clusters colauth column comment commit compress connected constant constraint crash create current currval cursor data_base database dba deallocate debugoff debugon declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry exception exception_init exchange exclusive exists external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base of off offline on online only option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw rebuild record ref references refresh rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\",\n    builtin: \"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define echo editfile embedded feedback flagger flush heading headsep instance linesize lno loboffset logsource longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar repfooter repheader serveroutput shiftinout show showmode spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout timing trimout trimspool ttitle underline verify version wrap\",\n    types: SQLTypes + \"ascii bfile bfilename bigserial bit blob dec long number nvarchar nvarchar2 serial smallint string text uid varchar2 xml\",\n    operatorChars: \"*/+-%<>!=~\",\n    doubleQuotedStrings: true,\n    charSetCasts: true,\n    plsqlQuotingMechanism: true\n});\n\nexport { Cassandra, MSSQL, MariaSQL, MySQL, PLSQL, PostgreSQL, SQLDialect, SQLite, StandardSQL, keywordCompletionSource, schemaCompletionSource, sql };\n"], "names": [], "sourceRoot": ""}