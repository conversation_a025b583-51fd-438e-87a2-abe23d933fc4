#!/usr/bin/env python3
"""
ChemProp 数据处理和可视化工具 - 针对 official_lipo_data.csv
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from rdkit import Chem
from rdkit.Chem import Descriptors, Draw
import warnings
import os
warnings.filterwarnings('ignore')

def find_data_file():
    """智能查找数据文件"""
    possible_paths = [
        'official_lipo_data.csv',  # 当前目录
        '../official_lipo_data.csv',  # 上级目录
        'Study/Chemprop/official_lipo_data.csv',  # 相对路径
        'D:/Code/Study/Chemprop/official_lipo_data.csv'  # 绝对路径
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到数据文件: {path}")
            return path
    
    print("❌ 未找到 official_lipo_data.csv 文件")
    print("请确保文件在以下位置之一:")
    for path in possible_paths:
        print(f"  - {path}")
    return None

class MolecularDataProcessor:
    """分子数据处理器"""
    
    def __init__(self, csv_file):
        """初始化"""
        self.csv_file = csv_file
        self.df = None
        self.valid_mols = []
        self.descriptors_df = None
    
    def load_data(self):
        """加载数据"""
        print(f"📊 加载数据: {self.csv_file}")
        self.df = pd.read_csv(self.csv_file)
        print(f"数据集大小: {len(self.df)} 个分子")
        print(f"数据列: {list(self.df.columns)}")
        
        # 显示基本统计信息
        if 'lipo' in self.df.columns:
            print(f"Lipophilicity 范围: {self.df['lipo'].min():.2f} ~ {self.df['lipo'].max():.2f}")
            print(f"Lipophilicity 平均值: {self.df['lipo'].mean():.2f}")
            print(f"Lipophilicity 标准差: {self.df['lipo'].std():.2f}")
        
        return self.df
    
    def validate_smiles(self):
        """验证 SMILES 有效性"""
        print("\n🔍 验证 SMILES 有效性...")
        
        invalid_smiles = []
        self.valid_mols = []
        
        for i, smiles in enumerate(self.df['smiles']):
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                invalid_smiles.append((i, smiles))
            else:
                self.valid_mols.append(mol)
        
        if invalid_smiles:
            print(f"❌ 发现 {len(invalid_smiles)} 个无效 SMILES:")
            for idx, smiles in invalid_smiles[:10]:  # 只显示前10个
                print(f"  行 {idx}: {smiles}")
            if len(invalid_smiles) > 10:
                print(f"  ... 还有 {len(invalid_smiles) - 10} 个无效 SMILES")
        else:
            print("✅ 所有 SMILES 都有效")
        
        return len(invalid_smiles) == 0
    
    def calculate_descriptors(self):
        """计算分子描述符"""
        print("\n🧮 计算分子描述符...")
        
        descriptors = []
        for mol in self.valid_mols:
            desc = {
                'MW': Descriptors.MolWt(mol),
                'LogP': Descriptors.MolLogP(mol),
                'TPSA': Descriptors.TPSA(mol),
                'HBD': Descriptors.NumHDonors(mol),
                'HBA': Descriptors.NumHAcceptors(mol),
                'RotBonds': Descriptors.NumRotatableBonds(mol),
                'NumAtoms': mol.GetNumAtoms(),
                'NumBonds': mol.GetNumBonds(),
                'NumRings': Descriptors.RingCount(mol),
                'Aromatic': Descriptors.NumAromaticRings(mol)
            }
            descriptors.append(desc)
        
        self.descriptors_df = pd.DataFrame(descriptors)
        print(f"✅ 计算了 {len(self.descriptors_df.columns)} 个描述符")
        return self.descriptors_df
    
    def data_summary(self):
        """数据摘要统计"""
        print("\n📈 数据摘要统计:")
        
        if self.descriptors_df is not None:
            combined_df = pd.concat([self.df, self.descriptors_df], axis=1)
            
            # 只对数值列进行统计
            numeric_cols = combined_df.select_dtypes(include=[np.number]).columns
            summary = combined_df[numeric_cols].describe()
            print(summary)
            
            return combined_df
        else:
            print("请先计算分子描述符")
            return None
    
    def visualize_data(self, save_plots=True):
        """数据可视化"""
        print("\n📊 生成数据可视化...")
        
        if self.descriptors_df is None:
            print("请先计算分子描述符")
            return
        
        combined_df = pd.concat([self.df, self.descriptors_df], axis=1)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建子图
        fig, axes = plt.subplots(3, 3, figsize=(15, 12))
        fig.suptitle('Official Lipo Dataset Analysis', fontsize=16)
        
        # 1. Lipophilicity 分布
        axes[0,0].hist(combined_df['lipo'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0,0].set_title('Lipophilicity Distribution')
        axes[0,0].set_xlabel('Lipophilicity')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. 分子量分布
        axes[0,1].hist(combined_df['MW'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0,1].set_title('Molecular Weight Distribution')
        axes[0,1].set_xlabel('MW (Da)')
        axes[0,1].set_ylabel('Frequency')
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 原子数分布
        axes[0,2].hist(combined_df['NumAtoms'], bins=15, alpha=0.7, color='orange', edgecolor='black')
        axes[0,2].set_title('Number of Atoms Distribution')
        axes[0,2].set_xlabel('Number of Atoms')
        axes[0,2].set_ylabel('Frequency')
        axes[0,2].grid(True, alpha=0.3)
        
        # 4. Lipophilicity vs 分子量
        axes[1,0].scatter(combined_df['MW'], combined_df['lipo'], alpha=0.6, s=30)
        axes[1,0].set_title('Lipophilicity vs Molecular Weight')
        axes[1,0].set_xlabel('MW (Da)')
        axes[1,0].set_ylabel('Lipophilicity')
        axes[1,0].grid(True, alpha=0.3)
        
        # 5. Lipophilicity vs LogP
        axes[1,1].scatter(combined_df['LogP'], combined_df['lipo'], alpha=0.6, s=30, color='red')
        axes[1,1].set_title('Lipophilicity vs Calculated LogP')
        axes[1,1].set_xlabel('Calculated LogP')
        axes[1,1].set_ylabel('Experimental Lipophilicity')
        axes[1,1].grid(True, alpha=0.3)
        
        # 添加对角线
        min_val = min(combined_df['LogP'].min(), combined_df['lipo'].min())
        max_val = max(combined_df['LogP'].max(), combined_df['lipo'].max())
        axes[1,1].plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='y=x')
        axes[1,1].legend()
        
        # 6. TPSA 分布
        axes[1,2].hist(combined_df['TPSA'], bins=15, alpha=0.7, color='pink', edgecolor='black')
        axes[1,2].set_title('TPSA Distribution')
        axes[1,2].set_xlabel('TPSA (Ų)')
        axes[1,2].set_ylabel('Frequency')
        axes[1,2].grid(True, alpha=0.3)
        
        # 7. 氢键供体/受体
        axes[2,0].scatter(combined_df['HBD'], combined_df['HBA'], alpha=0.6, s=30, color='purple')
        axes[2,0].set_title('H-Bond Donors vs Acceptors')
        axes[2,0].set_xlabel('H-Bond Donors')
        axes[2,0].set_ylabel('H-Bond Acceptors')
        axes[2,0].grid(True, alpha=0.3)
        
        # 8. 可旋转键分布
        axes[2,1].hist(combined_df['RotBonds'], bins=12, alpha=0.7, color='brown', edgecolor='black')
        axes[2,1].set_title('Rotatable Bonds Distribution')
        axes[2,1].set_xlabel('Number of Rotatable Bonds')
        axes[2,1].set_ylabel('Frequency')
        axes[2,1].grid(True, alpha=0.3)
        
        # 9. 相关性热图 - 只使用数值列
        corr_cols = ['lipo', 'MW', 'LogP', 'TPSA', 'HBD', 'HBA', 'NumAtoms', 'RotBonds']
        corr_matrix = combined_df[corr_cols].corr()
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[2,2], 
                   fmt='.2f', square=True, cbar_kws={'shrink': 0.8})
        axes[2,2].set_title('Property Correlations')
        
        plt.tight_layout()
        
        if save_plots:
            plt.savefig('lipo_dataset_analysis.png', dpi=300, bbox_inches='tight')
            print("✅ 图表已保存: lipo_dataset_analysis.png")
        
        plt.show()
    
    def lipinski_analysis(self):
        """Lipinski 五规则分析"""
        print("\n💊 Lipinski 五规则分析:")
        
        if self.descriptors_df is None:
            print("请先计算分子描述符")
            return
        
        combined_df = pd.concat([self.df, self.descriptors_df], axis=1)
        
        # Lipinski 规则
        rules = {
            'MW <= 500': combined_df['MW'] <= 500,
            'LogP <= 5': combined_df['LogP'] <= 5,
            'HBD <= 5': combined_df['HBD'] <= 5,
            'HBA <= 10': combined_df['HBA'] <= 10
        }
        
        print("Lipinski 五规则符合情况:")
        for rule, condition in rules.items():
            passed = condition.sum()
            total = len(condition)
            percentage = (passed / total) * 100
            print(f"  {rule}: {passed}/{total} ({percentage:.1f}%)")
        
        # 计算完全符合的分子
        all_rules = pd.concat(rules.values(), axis=1).all(axis=1)
        drug_like = all_rules.sum()
        total = len(all_rules)
        percentage = (drug_like / total) * 100
        
        print(f"\n🎯 完全符合 Lipinski 规则: {drug_like}/{total} ({percentage:.1f}%)")
        
        return combined_df[all_rules]
    
    def correlation_analysis(self):
        """相关性分析"""
        print("\n🔗 相关性分析:")
        
        if self.descriptors_df is None:
            print("请先计算分子描述符")
            return
        
        combined_df = pd.concat([self.df, self.descriptors_df], axis=1)
        
        # 只选择数值列进行相关性分析
        numeric_cols = combined_df.select_dtypes(include=[np.number]).columns
        numeric_df = combined_df[numeric_cols]
        
        # 计算与 lipophilicity 的相关性
        correlations = numeric_df.corr()['lipo'].sort_values(key=abs, ascending=False)
        
        print("与 Lipophilicity 的相关性 (按绝对值排序):")
        for prop, corr in correlations.items():
            if prop != 'lipo':
                print(f"  {prop}: {corr:.3f}")
        
        return correlations
    
    def export_processed_data(self, filename='processed_lipo_data.csv'):
        """导出处理后的数据"""
        print(f"\n💾 导出处理后的数据: {filename}")
        
        if self.descriptors_df is None:
            print("请先计算分子描述符")
            return
        
        combined_df = pd.concat([self.df, self.descriptors_df], axis=1)
        combined_df.to_csv(filename, index=False)
        print(f"✅ 数据已导出到: {filename}")
        
        return combined_df

def main():
    """主函数"""
    print("=" * 60)
    print("🧬 Official Lipo Dataset 分析工具")
    print("=" * 60)
    
    # 智能查找数据文件
    data_file = find_data_file()
    if data_file is None:
        return
    
    # 处理官方 lipo 数据集
    processor = MolecularDataProcessor(data_file)
    
    # 加载数据
    df = processor.load_data()
    
    # 验证 SMILES
    processor.validate_smiles()
    
    # 计算描述符
    processor.calculate_descriptors()
    
    # 数据摘要
    combined_df = processor.data_summary()
    
    # 相关性分析
    processor.correlation_analysis()
    
    # 可视化
    processor.visualize_data()
    
    # Lipinski 分析
    drug_like = processor.lipinski_analysis()
    
    # 导出处理后的数据
    processor.export_processed_data()
    
    print("\n🎉 数据处理完成!")
    print(f"📊 总共分析了 {len(df)} 个分子")
    print("📈 生成了详细的统计分析和可视化图表")
    print("💾 处理后的数据已保存")

if __name__ == "__main__":
    main()



