{"name": "@jupyter-notebook/lab-extension", "version": "7.4.4", "description": "Jupyter Notebook - Lab Extension", "homepage": "https://github.com/jupyter/notebook", "bugs": {"url": "https://github.com/jupyter/notebook/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyter/notebook.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/index.js"], "scripts": {"build": "jlpm run build:labextension:dev", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc -b", "build:prod": "jlpm run build:labextension", "clean": "jlpm run clean:lib && jlpm run clean:labextension", "clean:labextension": "rimraf ../../notebook/labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "watch": "run-p watch:src watch:labextension", "watch:labextension": "jupyter labextension watch .", "watch:src": "tsc -w"}, "dependencies": {"@jupyter-notebook/application": "^7.4.4", "@jupyterlab/application": "~4.4.4", "@jupyterlab/apputils": "~4.5.4", "@jupyterlab/coreutils": "~6.4.4", "@jupyterlab/docregistry": "~4.4.4", "@jupyterlab/notebook": "~4.4.4", "@jupyterlab/translation": "~4.4.4", "@jupyterlab/ui-components": "~4.4.4", "@lumino/commands": "^2.3.2", "@lumino/disposable": "^2.1.4"}, "devDependencies": {"@jupyterlab/builder": "~4.4.4", "rimraf": "^3.0.2", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "outputDir": "../../notebook/labextension", "schemaDir": "schema"}, "nx": {"targets": {"build:labextension:dev": {"dependsOn": ["^build:lib", "build:lib"], "outputs": ["{workspaceRoot}/notebook/labextension", "{workspaceRoot}/notebook/labextension/build_log.json"]}, "build:labextension": {"dependsOn": ["^build:lib", "build:lib"], "outputs": ["{workspaceRoot}/notebook/labextension"]}, "build": {"dependsOn": ["build:labextension:dev"]}, "build:prod": {"dependsOn": ["build:labextension"]}}}, "styleModule": "style/index.js"}