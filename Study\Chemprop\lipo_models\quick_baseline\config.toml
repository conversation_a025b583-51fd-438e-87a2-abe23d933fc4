batch-size = 50
data-path = official_lipo_data.csv
output-dir = lipo_models\quick_baseline
message-hidden-dim = 300
depth = 3
dropout = 0.0
task-type = regression
epochs = 20
split = RANDOM
split-sizes = [0.8, 0.1, 0.1]
data-seed = 42
num-workers = 0
accelerator = auto
devices = auto
rxn-mode = REAC_DIFF
multi-hot-atom-featurizer-mode = V2
frzn-ffn-layers = 0
ensemble-size = 1
aggregation = norm
aggregation-norm = 100
activation = RELU
ffn-hidden-dim = 300
ffn-num-layers = 1
multiclass-num-classes = 3
atom-ffn-hidden-dim = 300
atom-ffn-num-layers = 1
atom-multiclass-num-classes = 3
bond-ffn-hidden-dim = 300
bond-ffn-num-layers = 1
bond-multiclass-num-classes = 3
atom-constrainer-ffn-hidden-dim = 300
atom-constrainer-ffn-num-layers = 1
bond-constrainer-ffn-hidden-dim = 300
bond-constrainer-ffn-num-layers = 1
v-kl = 0.0
eps = 1e-08
alpha = 0.1
tracking-metric = val_loss
warmup-epochs = 2
init-lr = 0.0001
max-lr = 0.001
final-lr = 0.0001
split-key-molecule = 0
num-replicates = 1
